{"backtraceGraph": {"commands": ["install"], "files": ["C:/Libraries/zlib131/zlib-1.3.1/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 182, "parent": 0}, {"command": 0, "file": 0, "line": 188, "parent": 0}, {"command": 0, "file": 0, "line": 191, "parent": 0}, {"command": 0, "file": 0, "line": 194, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "C:/Program Files (x86)/OrderManager/lib", "paths": ["zlib/libzlib.dll.a"], "targetId": "zlib::@036b525f80ea8433013a", "targetIndex": 7, "targetIsImportLibrary": true, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "C:/Program Files (x86)/OrderManager/bin", "paths": ["zlib/libzlib.dll"], "targetId": "zlib::@036b525f80ea8433013a", "targetIndex": 7, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "C:/Program Files (x86)/OrderManager/lib", "paths": ["zlib/libzlibstatic.a"], "targetId": "zlibstatic::@036b525f80ea8433013a", "targetIndex": 8, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "C:/Program Files (x86)/OrderManager/include", "paths": ["build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zconf.h", "C:/Libraries/zlib131/zlib-1.3.1/zlib.h"], "type": "file"}, {"backtrace": 3, "component": "Unspecified", "destination": "C:/Program Files (x86)/OrderManager/share/man/man3", "paths": ["C:/Libraries/zlib131/zlib-1.3.1/zlib.3"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "C:/Program Files (x86)/OrderManager/share/pkgconfig", "paths": ["build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib.pc"], "type": "file"}], "paths": {"build": "zlib", "source": "C:/Libraries/zlib131/zlib-1.3.1"}}