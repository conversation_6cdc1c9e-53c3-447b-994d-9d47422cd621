{"artifacts": [{"path": "OrderManager.exe"}, {"path": "OrderManager.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "target_compile_options", "add_definitions", "target_include_directories"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 264, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 388, "parent": 0}, {"command": 7, "file": 1, "line": 24, "parent": 0}, {"command": 7, "file": 4, "line": 297, "parent": 6}, {"file": 3, "parent": 7}, {"command": 6, "file": 3, "line": 55, "parent": 8}, {"file": 2, "parent": 9}, {"command": 5, "file": 2, "line": 61, "parent": 10}, {"command": 6, "file": 3, "line": 43, "parent": 8}, {"file": 9, "parent": 12}, {"command": 9, "file": 9, "line": 45, "parent": 13}, {"command": 8, "file": 8, "line": 137, "parent": 14}, {"command": 7, "file": 7, "line": 76, "parent": 15}, {"command": 7, "file": 4, "line": 315, "parent": 16}, {"file": 6, "parent": 17}, {"command": 6, "file": 6, "line": 55, "parent": 18}, {"file": 5, "parent": 19}, {"command": 5, "file": 5, "line": 61, "parent": 20}, {"command": 4, "file": 0, "line": 640, "parent": 2}, {"command": 8, "file": 8, "line": 137, "parent": 14}, {"command": 7, "file": 7, "line": 76, "parent": 23}, {"command": 7, "file": 4, "line": 315, "parent": 24}, {"file": 11, "parent": 25}, {"command": 6, "file": 11, "line": 57, "parent": 26}, {"file": 10, "parent": 27}, {"command": 5, "file": 10, "line": 61, "parent": 28}, {"command": 6, "file": 11, "line": 45, "parent": 26}, {"file": 14, "parent": 30}, {"command": 9, "file": 14, "line": 46, "parent": 31}, {"command": 8, "file": 8, "line": 137, "parent": 32}, {"command": 7, "file": 7, "line": 76, "parent": 33}, {"command": 7, "file": 4, "line": 315, "parent": 34}, {"file": 13, "parent": 35}, {"command": 6, "file": 13, "line": 55, "parent": 36}, {"file": 12, "parent": 37}, {"command": 5, "file": 12, "line": 61, "parent": 38}, {"command": 5, "file": 12, "line": 83, "parent": 38}, {"command": 10, "file": 1, "line": 349, "parent": 0}, {"command": 11, "file": 1, "line": 127, "parent": 0}, {"command": 11, "file": 1, "line": 105, "parent": 0}, {"command": 11, "file": 1, "line": 103, "parent": 0}, {"command": 12, "file": 1, "line": 379, "parent": 0}, {"command": 12, "file": 1, "line": 402, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -fdiagnostics-color=always"}, {"backtrace": 41, "fragment": "-Wall"}, {"backtrace": 41, "fragment": "-Wextra"}, {"backtrace": 41, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 22, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 42, "define": "NDEBUG"}, {"backtrace": 42, "define": "NOMINMAX"}, {"backtrace": 43, "define": "OPENSSL_3_PLUS"}, {"backtrace": 44, "define": "OPENSSL_FOUND"}, {"backtrace": 5, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 22, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 22, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 22, "define": "UNICODE"}, {"backtrace": 22, "define": "WIN32"}, {"backtrace": 42, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 22, "define": "WIN64"}, {"backtrace": 42, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 22, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 42, "define": "_HAS_ITERATOR_DEBUGGING=0"}, {"backtrace": 42, "define": "_SECURE_SCL=0"}, {"backtrace": 22, "define": "_UNICODE"}, {"backtrace": 22, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include"}, {"backtrace": 45, "path": "C:/Program Files/OpenSSL-Win64/include"}, {"backtrace": 46, "path": "C:/eee/cc"}, {"backtrace": 46, "path": "C:/eee/cc/src"}, {"backtrace": 46, "path": "C:/eee/cc/src/core"}, {"backtrace": 46, "path": "C:/eee/cc/src/core/utils"}, {"backtrace": 46, "path": "C:/eee/cc/src/core/models"}, {"backtrace": 46, "path": "C:/eee/cc/src/core/services"}, {"backtrace": 46, "path": "C:/eee/cc/src/core/controllers"}, {"backtrace": 46, "path": "C:/eee/cc/src/network"}, {"backtrace": 46, "path": "C:/eee/cc/src/network/engines"}, {"backtrace": 46, "path": "C:/eee/cc/src/integration"}, {"backtrace": 46, "path": "C:/eee/cc/src/config"}, {"backtrace": 46, "path": "C:/eee/cc/src/ui"}, {"backtrace": 46, "path": "C:/eee/cc/legacy"}, {"backtrace": 46, "path": "C:/eee/cc/legacy/api"}, {"backtrace": 46, "path": "C:/eee/cc/legacy/network"}, {"backtrace": 46, "path": "C:/eee/cc/legacy/services"}, {"backtrace": 46, "path": "C:/eee/cc/legacy/utils"}, {"backtrace": 46, "path": "C:/eee/cc/legacy/workers"}, {"backtrace": 5, "path": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib"}, {"backtrace": 5, "path": "C:/Libraries/zlib131/zlib-1.3.1"}, {"backtrace": 22, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore"}, {"backtrace": 22, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include"}, {"backtrace": 22, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtConcurrent"}], "language": "CXX", "sourceIndexes": [0, 1, 3, 6, 9, 11, 13, 15, 17, 19, 21, 24, 26, 28, 30, 32, 34, 37, 39, 42, 46]}], "dependencies": [{"backtrace": 5, "id": "zlibstatic::@036b525f80ea8433013a"}, {"id": "OrderManager_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "OrderManager_autogen::@6890427a1f51a3e7e1df"}], "id": "OrderManager::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "-mwindows", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Widgets.a", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Network.a", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Concurrent.a", "role": "libraries"}, {"backtrace": 5, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 5, "fragment": "-lcrypt32", "role": "libraries"}, {"backtrace": 5, "fragment": "zlib\\libzlibstatic.a", "role": "libraries"}, {"backtrace": 5, "fragment": "\"C:\\Program Files\\OpenSSL-Win64\\lib\\VC\\x64\\MD\\libssl.lib\"", "role": "libraries"}, {"backtrace": 5, "fragment": "\"C:\\Program Files\\OpenSSL-Win64\\lib\\VC\\x64\\MD\\libcrypto.lib\"", "role": "libraries"}, {"backtrace": 11, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 21, "fragment": "-ld3d11", "role": "libraries"}, {"backtrace": 21, "fragment": "-ldxgi", "role": "libraries"}, {"backtrace": 21, "fragment": "-ldxguid", "role": "libraries"}, {"backtrace": 21, "fragment": "-ld3d12", "role": "libraries"}, {"backtrace": 22, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 29, "fragment": "-lmpr", "role": "libraries"}, {"backtrace": 29, "fragment": "-l<PERSON><PERSON>v", "role": "libraries"}, {"backtrace": 39, "fragment": "-lmingw32", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6EntryPoint.a", "role": "libraries"}, {"backtrace": 40, "fragment": "-lshell32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "OrderManager", "nameOnDisk": "OrderManager.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0, 47, 48]}, {"name": "Source Files", "sourceIndexes": [1, 3, 6, 9, 11, 13, 15, 17, 19, 21, 24, 26, 28, 30, 32, 34, 37, 39, 42, 46]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 5, 7, 8, 10, 12, 14, 16, 18, 20, 22, 23, 25, 27, 29, 31, 33, 35, 36, 38, 40, 41, 43, 44, 45]}, {"name": "Forms", "sourceIndexes": [4]}, {"name": "CMake Rules", "sourceIndexes": [49]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/mainwindow.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/mainwindow.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/mainwindow.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "legacy/api/orderapi.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/api/orderapi.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/api/api_constants.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "legacy/workers/filterworker.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/workers/filterworker.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/network/ultrafasttls.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/network/ultrafasttls.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/network/ultrafasttls_debug_monitor.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/network/ultrafasttls_debug_monitor.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/network/network_client.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/network/network_client.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/network/request_builder.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/network/request_builder.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/network/response_processor.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/network/response_processor.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/utils/simple_logger.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/utils/simple_logger.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/utils/utils.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "legacy/utils/error_handler.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/utils/error_handler.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/utils/json_parser.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/utils/json_parser.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/services/encryption_service.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/services/encryption_service.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/services/authentication_service.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/services/authentication_service.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/services/service_container.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/services/service_container.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "legacy/services/business_logic.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "legacy/services/business_logic.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "functional_utils.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "src/config/app_config.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/config/app_config.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/core/utils/logger.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/core/utils/logger.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/network/engines/network_engine_interface.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "src/network/engines/ultrafasttls_adapter.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/network/engines/ultrafasttls_adapter.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/core/utils/json_helper.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "src/core/utils/string_utils.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "src/integration/legacy_api_adapter.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/integration/legacy_api_adapter.cpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include/src/ui/ui_mainwindow.h", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}