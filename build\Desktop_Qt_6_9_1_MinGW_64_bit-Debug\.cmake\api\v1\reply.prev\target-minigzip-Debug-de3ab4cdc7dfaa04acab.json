{"artifacts": [{"path": "zlib/minigzip.exe"}, {"path": "zlib/minigzip.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "add_definitions", "include_directories"], "files": ["C:/Libraries/zlib131/zlib-1.3.1/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 205, "parent": 0}, {"command": 1, "file": 0, "line": 206, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 127, "parent": 3}, {"command": 2, "file": 1, "line": 105, "parent": 3}, {"command": 2, "file": 1, "line": 103, "parent": 3}, {"command": 2, "file": 0, "line": 44, "parent": 0}, {"command": 3, "file": 0, "line": 85, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -fdiagnostics-color=always"}], "defines": [{"backtrace": 4, "define": "NDEBUG"}, {"backtrace": 4, "define": "NOMINMAX"}, {"backtrace": 5, "define": "OPENSSL_3_PLUS"}, {"backtrace": 6, "define": "OPENSSL_FOUND"}, {"backtrace": 4, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 4, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 4, "define": "_HAS_ITERATOR_DEBUGGING=0"}, {"backtrace": 7, "define": "_LARGEFILE64_SOURCE=1"}, {"backtrace": 4, "define": "_SECURE_SCL=0"}], "includes": [{"backtrace": 8, "path": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib"}, {"backtrace": 8, "path": "C:/eee/cc"}, {"backtrace": 2, "path": "C:/Libraries/zlib131/zlib-1.3.1"}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "zlib::@036b525f80ea8433013a"}], "id": "minigzip::@036b525f80ea8433013a", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "zlib\\libzlib.dll.a", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "C"}, "name": "minigzip", "nameOnDisk": "minigzip.exe", "paths": {"build": "zlib", "source": "C:/Libraries/zlib131/zlib-1.3.1"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/test/minigzip.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}