# ninja log v6
5719	10173	7755915560298498	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
21	1680	7755915503319390	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
1217	2548	7755915515285616	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
12503	23199	****************	zlib/example64.exe	4a92267bf6f9e4ab
5274	38129	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
1034	2547	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
4032	6149	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
144	1834	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
15	118	****************	CMakeFiles/clean.additional	baedbe210acaa455
367	1998	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
5274	38129	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
553	2191	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
12500	23763	****************	zlib/minigzip.exe	b30485a50c6b3a85
701	2357	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
12506	23554	****************	zlib/minigzip64.exe	4f10a1aa1eb3582e
2544	3386	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
855	2544	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
12498	23860	****************	zlib/example.exe	61852f41d57c7507
1366	2548	****************	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
1523	2549	****************	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
1681	2549	7755915519914043	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
1834	2550	7755915521443705	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
4324	7275	7755915546352418	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
3715	5277	7755915540256275	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
1998	2722	7755915523090688	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
2191	2815	7755915525016006	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
2357	3007	7755915526678816	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
2544	3386	****************	zlib/zlib1rc.obj	5625031d43e5c7be
3386	4829	7755915536964535	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
4183	6699	7755915544941906	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
3571	5032	7755915538818284	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
3007	5274	7755915533172578	zlib/libzlibstatic.a	22b16c9e053b901
3873	5719	7755915541836570	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
4486	8126	7755915547966106	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
4656	8943	7755915549675840	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
4830	9617	7755915551401599	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
5032	10170	7755915553430852	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
38130	66662	7755915884407809	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
5274	38129	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
5277	10171	7755915555877048	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
6149	10174	7755915564594091	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
6699	10175	7755915570093248	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
7275	10179	7755915575862371	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
8127	10421	7755915584379489	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
38324	64887	7755915886352378	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
8944	11193	7755915592544648	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
9617	11732	7755915599278406	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
10175	12497	7755915627953261	zlib/libzlib.dll	c70d7c7719fed2bf
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
10175	12497	7755915627953261	zlib/libzlib.dll.a	c70d7c7719fed2bf
5274	38129	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
24	4460	7755915206896040	OrderManager.exe	130079037154008e
15	3357	7755841096121232	build.ninja	497fbd042d0239fc
118	269	7755915489683573	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
115513	199882	7755816077387186	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
123886	199819	7755816161131923	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
38586	71922	7755915888962610	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
128335	198576	7755816205622297	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
39163	52739	7755915894741336	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
40015	57513	7755915903262878	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
39549	63407	7755915898601254	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
************	7755815888875690	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
102382	196688	7755815946093797	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
105742	196689	7755815979686004	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
40899	54259	7755915912106504	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
41446	59702	7755915917580490	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
1038	12908	7755929938404482	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
106719	199113	7755815989463114	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
119800	199793	7755816120274133	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
40468	58747	7755915907796631	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
112088	198197	7755816043147868	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
5899	29661	7755918972331445	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
5774	28282	7755918971078123	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
29	1087	7755919948111094	OrderManager_autogen/timestamp	21ad19f5d651830a
29	1087	7755919948111094	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
29	1087	7755919948111094	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
29	1087	7755919948111094	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1262	16062	7755919960438319	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
2474	21146	7755919972556447	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
1815	22040	7755919965970630	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
2126	22864	7755919969079557	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
1514	23101	7755919962958047	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
2879	23478	7755919976600552	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
3293	24016	7755919980751448	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
4562	28436	7755919993443329	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
3871	29373	7755919986525916	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
1088	32995	7755919958704072	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
16062	34447	7755920108446208	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
21146	35134	7755920159279795	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
35135	41181	7755920299160875	OrderManager.exe	130079037154008e
25	239	7755926795266615	CMakeFiles/clean.additional	baedbe210acaa455
240	427	7755926797414551	clean	577b940aee94fbd7
16	290	7755926813229323	CMakeFiles/clean.additional	baedbe210acaa455
291	486	7755926815989763	clean	577b940aee94fbd7
36	5018	7755926841311005	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
246	5659	7755926843414506	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
518	6381	7755926846137631	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
829	7045	7755926849242589	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
1239	7702	7755926853362712	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
1756	8287	7755926858524322	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
2327	8297	7755926864226209	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
3072	8299	7755926871688831	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
3694	8303	7755926877903218	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
4419	8304	7755926885148633	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
5019	8306	7755926891151223	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
5659	8308	7755926897549474	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
6382	8958	7755926904773770	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
7045	9287	7755926911410032	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
7702	9894	7755926917982639	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
8288	10769	7755926948024629	zlib/zlib1rc.obj	5625031d43e5c7be
8288	10769	7755926948024629	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
10770	16215	7755926948657627	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
11331	17042	7755926954268433	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
9895	17558	7755926939907383	zlib/libzlibstatic.a	22b16c9e053b901
11863	17567	7755926959588915	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
12414	18377	7755926965092087	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
13042	19059	7755926971386642	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
13617	19750	7755926977126413	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
14152	21602	7755926982487797	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
15088	22176	7755926991843243	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
15667	22885	7755926997638330	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
16215	23616	7755927003112508	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
17042	24390	7755927011376283	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
17568	24392	7755927016640726	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
18377	24393	7755927024734331	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
19059	24395	7755927031548365	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
19750	24397	7755927038469123	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
21602	24405	7755927056992668	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
22177	24909	7755927062731637	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
22885	25585	7755927069815368	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
23617	26359	7755927077130858	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
24398	27231	7755927113011721	zlib/libzlib.dll	c70d7c7719fed2bf
24398	27231	7755927113011721	zlib/libzlib.dll.a	c70d7c7719fed2bf
27250	59934	7755927113463123	zlib/minigzip64.exe	4f10a1aa1eb3582e
27233	60226	7755927113291759	zlib/example.exe	61852f41d57c7507
27239	60467	7755927113355608	zlib/minigzip.exe	b30485a50c6b3a85
27245	60503	7755927113408542	zlib/example64.exe	4a92267bf6f9e4ab
17558	74555	7755927582462963	OrderManager_autogen/timestamp	21ad19f5d651830a
17558	74555	7755927582462963	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
17558	74555	7755927582462963	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
17558	74555	7755927582462963	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
75917	89140	7755927600123125	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
77516	91423	7755927616123098	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
76713	98387	7755927608084650	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
89141	105093	7755927732400413	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
77083	105719	7755927611787393	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
91425	106378	7755927755225885	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
78027	107329	7755927621230962	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
76316	157021	7755927604121079	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
74811	174562	7755927589069184	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
105094	176628	7755927891905089	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
98390	194295	7755927824886167	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
74558	204826	7755927586536009	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
************	7755927596427281	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
************	7755927592565944	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
105719	204833	7755927898153262	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
107330	206430	7755927914258015	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
106379	206467	7755927904762478	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
174563	219017	7755928586588660	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
157023	220049	7755928411198420	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
176629	223743	7755928607246747	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
194296	224378	7755928783917925	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
224378	233850	7755929084744248	OrderManager.exe	130079037154008e
22	926	7755931874998046	OrderManager_autogen/timestamp	21ad19f5d651830a
22	926	7755931874998046	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
22	926	7755931874998046	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
22	926	7755931874998046	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
927	6183	7755931883971736	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
21	4409	7755932427501075	OrderManager.exe	130079037154008e
