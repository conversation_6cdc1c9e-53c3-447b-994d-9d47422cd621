# ninja log v6
93393	177548	****************	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
69	5610	****************	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
369	6267	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
26932	130551	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
33	1513	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
2890	9326	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
3481	9330	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
236430	311875	****************	zlib/example64.exe	4a92267bf6f9e4ab
767	6978	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
47	497	****************	CMakeFiles/clean.additional	baedbe210acaa455
1228	7795	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
33	1513	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1815	8577	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
236425	312216	****************	zlib/minigzip.exe	b30485a50c6b3a85
2322	9314	7755292829769820	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
9315	15149	7755292957917216	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
236437	311565	7755295170930304	zlib/minigzip64.exe	4f10a1aa1eb3582e
4141	9332	7755292847975386	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
236417	311220	7755295170727187	zlib/example.exe	61852f41d57c7507
4868	9334	7755292855229893	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
5612	9338	7755292862667095	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
6268	9343	7755292869222575	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
6979	10382	7755292876291432	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
16429	92624	7755292970840444	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
29706	138626	7755293103616482	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
7796	10700	7755292884510409	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
8578	12126	7755292892337448	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
9315	15149	7755292957917216	zlib/zlib1rc.obj	5625031d43e5c7be
15150	89771	7755292958050874	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
15759	91796	7755292964138946	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
28957	134416	7755293096123721	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
17196	93392	7755292978518244	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
12126	14872	7755292927814374	zlib/libzlibstatic.a	22b16c9e053b901
33414	169484	7755293140692923	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
73460	171392	7755293541149041	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
89772	172235	7755293704262889	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
91796	177545	7755293724514073	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
92624	177547	7755293732794277	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
33	1513	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
17013	70566	7755310734136421	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
130552	214809	7755294112066596	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
134417	219081	7755294150717217	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
138627	234685	7755294192815114	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
17290	65906	7755310736800168	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
169484	234305	7755294501389941	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
171392	234496	7755294520468689	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
172236	234601	7755294528911228	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
219082	236417	7755295170416052	zlib/libzlib.dll	c70d7c7719fed2bf
219082	236417	7755295170416052	zlib/libzlib.dll.a	c70d7c7719fed2bf
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
33	1513	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
38475	46523	7755177642565720	OrderManager.exe	130079037154008e
15	3357	7755318494118379	build.ninja	497fbd042d0239fc
499	883	7755292787104787	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
119248	139143	7755307534916201	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
439484	518316	7755297201383838	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
486926	515976	7755297675819111	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
1517	33357	7755313381048695	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
337687	392926	7755296183427693	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
339390	431290	7755296200458370	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
18577	55814	7755310749796479	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
************	7755306994687757	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
************	7755307015448700	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
************	7755307093564242	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
344682	433640	7755296253375172	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
************	7755306985017637	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
5447	41027	7755165250480450	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
110354	136959	7755307445985874	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
120087	146131	7755307543300676	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
************	7755306811476589	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
111185	138337	7755307454305070	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
136339	151566	7755307705834373	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
124453	148694	7755307586971126	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
40	27680	7755318786814943	OrderManager_autogen/timestamp	21ad19f5d651830a
40	27680	7755318786814943	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
40	27680	7755318786814943	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
40	27680	7755318786814943	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
30488	61872	7755318819382746	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
31784	64080	7755318832341268	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
30113	64930	7755318815619906	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
29520	65789	7755318809680357	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
29078	69661	7755318805281590	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
31100	71837	7755318825506630	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
27924	73672	7755318793755371	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
27681	78535	7755318791314495	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
64081	88525	7755319155322250	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
64931	91207	7755319163817358	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
65789	95492	7755319172396790	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
61873	95509	7755319133246320	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
28296	98899	7755318797437146	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
************	7755319211133320	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
************	7755319232884298	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
39	15515	7755321378054510	OrderManager_autogen/timestamp	21ad19f5d651830a
39	15515	7755321378054510	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
39	15515	7755321378054510	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
39	15515	7755321378054510	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
16994	41425	7755321397135446	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
17484	42605	7755321402040575	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
16675	43384	7755321393890562	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
15740	50851	7755321384580264	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
15516	57158	7755321382368760	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
16337	58792	7755321390581296	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
16056	75043	7755321387722834	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
102	42809	7755321744996126	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
1332	45170	7755321757306908	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
630	53228	7755321750255047	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
27	966	7755323000391750	OrderManager_autogen/timestamp	21ad19f5d651830a
27	966	7755323000391750	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
27	966	7755323000391750	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
27	966	7755323000391750	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
967	15335	7755323009689939	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
15335	24049	7755323153500865	OrderManager.exe	130079037154008e
20	746	7755323566259194	OrderManager_autogen/timestamp	21ad19f5d651830a
20	746	7755323566259194	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
20	746	7755323566259194	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
20	746	7755323566259194	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
747	16314	7755323573570589	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
16314	22449	7755323729210027	OrderManager.exe	130079037154008e
