# ninja log v6
5830	8039	****************	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
22	1808	****************	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
4351	6052	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
176	1986	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
5826	41406	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
1081	2736	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
10892	27835	****************	zlib/example64.exe	4a92267bf6f9e4ab
1273	2736	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
408	2192	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
15	198	****************	CMakeFiles/clean.additional	baedbe210acaa455
573	2368	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
5826	41406	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
751	2547	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
10888	27735	****************	zlib/minigzip.exe	b30485a50c6b3a85
914	2734	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
2734	3630	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
10897	28445	7755912308473279	zlib/minigzip64.exe	4f10a1aa1eb3582e
1448	2737	7755912213991173	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
10885	27703	7755912308356948	zlib/example.exe	61852f41d57c7507
1633	2737	7755912215832707	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
1809	2738	7755912217587022	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
1986	2738	7755912219365432	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
2192	2903	7755912221422413	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
3980	5644	7755912239305007	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
4723	6508	7755912246729408	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
2368	3035	7755912223177573	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
2547	3283	7755912224978395	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
2734	3630	****************	zlib/zlib1rc.obj	5625031d43e5c7be
3630	5286	7755912235799879	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
3810	5476	7755912237609592	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
4550	6278	7755912245006026	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
4155	5829	7755912241047152	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
3283	5826	7755912232312286	zlib/libzlibstatic.a	22b16c9e053b901
4929	6753	7755912248795098	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
5106	7018	7755912250559890	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
5286	7406	7755912252312980	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
5477	8037	7755912254265981	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
5644	8038	7755912255944278	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
5826	41406	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
41407	91653	7755912613569779	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
6052	8040	7755912260015638	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
6278	8040	7755912262286271	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
6508	8043	7755912264585810	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
41625	86206	7755912615758877	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
6753	8387	7755912267027806	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
7018	9182	7755912269683954	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
7406	10052	7755912273571052	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
8040	10884	7755912308174328	zlib/libzlib.dll	c70d7c7719fed2bf
8040	10884	7755912308174328	zlib/libzlib.dll.a	c70d7c7719fed2bf
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
5826	41406	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
119922	126046	7755913398722943	OrderManager.exe	130079037154008e
15	3357	7755841096121232	build.ninja	497fbd042d0239fc
198	368	7755912191527827	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
************	7755913031377025	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
************	7755913116049581	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
************	7755913146227734	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
************	7755912618682440	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
42586	57732	7755912625364463	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
43469	68120	7755912634202010	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
42966	83186	7755912629163565	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
57733	94672	7755912776828851	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
65057	98585	7755912850069590	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
************	7755912880711344	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
44484	65056	7755912644342467	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
45283	74475	7755912652342072	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
************	7755912621805036	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
************	7755912912015927	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
************	7755913061574195	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
43949	71250	7755912638989712	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
************	7755912944268880	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
100303	119922	7755913202538451	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
************	7755913185358774	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
19	187	7755935139563508	CMakeFiles/clean.additional	baedbe210acaa455
14	213	7755935177415803	CMakeFiles/clean.additional	baedbe210acaa455
24	2006	7755935207910774	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
218	2171	7755935209901800	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
501	2365	7755935212733584	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
714	2585	7755935214860080	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
896	2768	7755935216680798	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
1066	2942	7755935218377481	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
1252	2944	7755935220245185	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
1464	2944	7755935222319301	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
1651	2945	7755935224236168	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
1820	2945	7755935225926884	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
2006	2946	7755935227779773	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
2171	2946	7755935229440281	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
2365	3124	7755935231378379	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
2586	3226	7755935233579525	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
2768	3521	7755935235400875	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
2942	3836	7755935246031774	zlib/zlib1rc.obj	5625031d43e5c7be
2942	3836	7755935246031774	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
3836	5599	7755935246082043	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
4081	5792	7755935248554601	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
4265	6011	7755935250377146	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
3522	6315	7755935242934337	zlib/libzlibstatic.a	22b16c9e053b901
4450	6319	7755935252225410	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
4645	6851	7755935254175027	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
4832	7374	7755935256044408	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
4999	8092	7755935257714082	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
5197	8718	7755935259688466	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
5381	9369	7755935261532966	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
5599	10062	7755935263713695	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
5792	10821	7755935265639245	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
6012	10822	7755935267835306	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
6320	10822	7755935270921934	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
6851	10823	7755935276241565	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
7374	10824	7755935281461874	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
8092	10829	7755935288643451	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
8718	11178	7755935294900861	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
9369	11915	7755935301415770	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
10062	12566	7755935308342180	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
10825	13296	7755935340535908	zlib/libzlib.dll	c70d7c7719fed2bf
10825	13296	7755935340535908	zlib/libzlib.dll.a	c70d7c7719fed2bf
13299	39184	7755935340712481	zlib/minigzip.exe	b30485a50c6b3a85
13304	40316	7755935340765193	zlib/minigzip64.exe	4f10a1aa1eb3582e
13297	40372	7755935340689518	zlib/example.exe	61852f41d57c7507
13301	40708	7755935340733063	zlib/example64.exe	4a92267bf6f9e4ab
6315	50341	7755935707717133	OrderManager_autogen/timestamp	21ad19f5d651830a
6315	50341	7755935707717133	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
6315	50341	7755935707717133	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
6315	50341	7755935707717133	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
51492	69168	7755935722645359	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
53462	78609	7755935742332712	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
52345	79415	7755935731182024	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
52915	80176	7755935736884580	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
54025	81659	7755935747969953	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
50572	121635	7755935713441640	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
51905	132986	7755935726774228	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
50342	133473	7755935711143412	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
69168	133921	7755935899406368	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
51161	134379	7755935719328233	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
80176	134941	7755936009489126	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
78610	135471	7755935993823153	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
81659	135473	7755936024324200	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
************	7755936001877221	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
************	7755935716260837	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
121635	145640	7755936424078729	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
133921	146502	7755936546935683	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
132986	146600	7755936537582150	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
133473	147008	7755936542451101	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
134379	149743	7755936551524252	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
134941	150156	7755936557131120	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
150156	156340	7755936709276656	OrderManager.exe	130079037154008e
13	175	7755907340367857	CMakeFiles/clean.additional	baedbe210acaa455
175	338	7755907341983738	clean	577b940aee94fbd7
22	1834	7755907347857219	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
148	2025	7755907349114812	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
378	2212	7755907351411552	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
547	2420	7755907353100571	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
727	2671	7755907354903478	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
906	3030	7755907356686287	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
1060	3034	7755907358236954	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
1281	3034	7755907360443728	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
1467	3035	7755907362304875	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
1651	3036	7755907364143199	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
1835	3037	7755907365984683	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
2025	3037	7755907367889805	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
2212	3416	7755907369758056	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
2420	3723	7755907371828369	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
2671	4157	7755907374346255	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
3030	4705	7755907394637012	zlib/zlib1rc.obj	5625031d43e5c7be
3030	4705	7755907394637012	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
4706	8949	7755907394695092	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
5045	9424	7755907398087661	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
4157	9884	7755907389204875	zlib/libzlibstatic.a	22b16c9e053b901
5458	9888	7755907402222793	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
5926	10579	7755907406891729	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
6383	12474	7755907411462468	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
6883	14978	7755907416474724	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
7337	15830	7755907420996651	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
7877	16732	7755907426403819	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
8441	18618	7755907432050518	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
8950	19570	7755907437134933	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
9888	41315	7755907446512102	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
12474	56293	7755907472335527	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
10579	56948	7755907453428529	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
14978	58402	7755907497418748	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
9424	58518	7755907441874017	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
15831	58739	7755907505944930	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
18618	59113	7755907533816826	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
16733	59244	7755907514967107	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
19570	59537	7755907543341551	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
58519	60488	7755907952417792	zlib/libzlib.dll	c70d7c7719fed2bf
58519	60488	7755907952417792	zlib/libzlib.dll.a	c70d7c7719fed2bf
60488	66640	7755907952463524	zlib/example.exe	61852f41d57c7507
60489	66738	7755907952463524	zlib/minigzip.exe	b30485a50c6b3a85
60491	67586	7755907952546185	zlib/example64.exe	4a92267bf6f9e4ab
60493	67751	7755907952561289	zlib/minigzip64.exe	4f10a1aa1eb3582e
9884	86044	7755908204712781	OrderManager_autogen/timestamp	21ad19f5d651830a
9884	86044	7755908204712781	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
9884	86044	7755908204712781	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
9884	86044	7755908204712781	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
87211	106787	7755908219748817	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
89190	110488	7755908239546321	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
88617	153548	7755908233819290	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
88073	161879	7755908228366989	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
89740	164009	7755908245034021	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
86571	168442	7755908213342089	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
86245	178709	7755908210087295	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
86044	180737	7755908208081948	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
86889	198206	7755908216526354	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
87613	199112	7755908223770504	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
106791	239973	7755908415550241	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
110489	262193	7755908452523488	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
153548	262195	7755908883123752	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
168442	262197	7755909032063316	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
161879	262199	7755908966429862	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
164009	262201	7755908987728702	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
178710	277911	7755909134733550	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
198207	281117	7755909329707522	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
180738	281791	7755909155006559	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
199113	295610	7755909338766077	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
239974	297698	7755909747392098	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
297699	310980	7755910324617871	OrderManager.exe	130079037154008e
31	16885	7755911354421650	OrderManager_autogen/timestamp	21ad19f5d651830a
31	16885	7755911354421650	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
31	16885	7755911354421650	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
31	16885	7755911354421650	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
19334	50933	7755911386465768	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
18760	52902	7755911380729270	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
20031	54198	7755911393442645	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
17314	57971	7755911366261912	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
16890	60716	7755911362031470	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
17764	68626	7755911370760892	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
37	16534	7755913621341616	OrderManager_autogen/timestamp	21ad19f5d651830a
37	16534	7755913621341616	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
37	16534	7755913621341616	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
37	16534	7755913621341616	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
18728	47831	7755913647508482	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
18103	49570	7755913641263026	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
19421	49835	7755913654443191	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
16810	53252	7755913628324500	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
16535	56309	7755913625582620	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
17180	64929	7755913632024675	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
18	247	7755914660207680	CMakeFiles/clean.additional	baedbe210acaa455
248	504	7755914662502703	clean	577b940aee94fbd7
23	303	7755914682901183	CMakeFiles/clean.additional	baedbe210acaa455
303	572	7755914685708791	clean	577b940aee94fbd7
