# ninja log v6
10105	95437	7755927290932192	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
23	1790	****************	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
98114	150877	****************	zlib/example64.exe	4a92267bf6f9e4ab
1269	2743	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
53	1999	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
1045	2742	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
134	1981	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
4334	19614	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
14	180	****************	CMakeFiles/clean.additional	baedbe210acaa455
304	2170	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
53	1999	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
488	2343	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
98107	150735	****************	zlib/minigzip.exe	b30485a50c6b3a85
687	2537	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
98118	150524	****************	zlib/minigzip64.exe	4f10a1aa1eb3582e
2739	3620	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
858	2739	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
98102	151125	****************	zlib/example.exe	61852f41d57c7507
1430	2743	7755927204170707	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
1599	2744	7755927205864563	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
1790	2745	7755927207778720	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
1981	2745	7755927209684271	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
4680	61453	7755927236674749	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
3952	8084	7755927229392035	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
2170	2926	7755927211572596	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
2343	3030	7755927213308656	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
2537	3299	7755927215247309	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
2739	3620	****************	zlib/zlib1rc.obj	5625031d43e5c7be
3620	5644	7755927226071353	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
4510	20540	7755927234971177	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
3783	6037	7755927227712024	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
3300	8073	7755927222874155	zlib/libzlibstatic.a	22b16c9e053b901
4124	10105	7755927231106707	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
4916	78404	7755927239031215	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
5264	88256	7755927242504415	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
5645	94637	7755927246322856	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
6037	95435	7755927250251994	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
59	107125	7755929626186610	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
53	1999	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
8084	95436	7755927270721538	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
19615	95439	7755927386028615	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
20541	95915	7755927395281671	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
61453	95648	7755927804404977	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
78404	95440	7755927973919836	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
363	99783	7755929629211335	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
88256	96359	7755928072384691	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
94637	96728	7755928136249277	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
95915	98102	7755928170683912	zlib/libzlib.dll	c70d7c7719fed2bf
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
95915	98102	7755928170683912	zlib/libzlib.dll.a	c70d7c7719fed2bf
53	1999	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
148857	155330	7755931114155880	OrderManager.exe	130079037154008e
15	3357	7755841096121232	build.ninja	497fbd042d0239fc
180	341	7755927166278401	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
99784	148460	7755930623424636	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
105821	147667	7755930683794289	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
830	109592	7755929633885174	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
106300	148701	7755930688589630	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
161622	177706	7755928806097705	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
162467	187596	7755928814546164	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
2001	11553	7755932783561170	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
4802	105820	7755929673606133	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
5566	102873	7755929681245428	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
7603	108917	7755929701620843	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
163446	180482	7755928824339470	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
4102	106300	7755929666608462	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
1318	109586	7755929638757958	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
************	7755930103552095	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
102874	148627	7755930654326309	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
162924	229513	7755928819116896	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
************	7755930524737935	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
108917	148814	7755930714760872	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
107126	147866	7755930696845494	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
24	5832	7755933419144438	OrderManager.exe	130079037154008e
38	1919	7755934866021087	OrderManager_autogen/timestamp	21ad19f5d651830a
38	1919	7755934866021087	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
38	1919	7755934866021087	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
38	1919	7755934866021087	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1920	16241	7755934884845834	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
62	14113	7755934988175475	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
16242	30261	7755935028074716	OrderManager.exe	130079037154008e
14114	26265	7755935128686304	OrderManager.exe	130079037154008e
16	241	7755902266878825	CMakeFiles/clean.additional	baedbe210acaa455
241	465	7755902269125438	clean	577b940aee94fbd7
19	247	7755902294282758	CMakeFiles/clean.additional	baedbe210acaa455
248	466	7755902296567723	clean	577b940aee94fbd7
34	12714	7755902309513179	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
275	48837	7755902311915383	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
655	49670	7755902315715436	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
1089	50576	7755902320066513	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
1658	51515	7755902325757879	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
2189	53705	7755902331054798	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
3157	53717	7755902340744970	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
6735	53718	7755902376516607	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
7576	53719	7755902384927421	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
8537	89842	7755902394538167	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
12717	90935	7755902436345344	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
48837	130692	7755902797547140	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
49671	131702	7755902805875759	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
50577	132199	7755902814946791	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
51515	132660	7755902824321448	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
132660	134931	7755903635769502	zlib/libzlibstatic.a	22b16c9e053b901
53706	134954	7755903658634692	zlib/zlib1rc.obj	5625031d43e5c7be
53706	134954	7755903658634692	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
134955	140263	7755903658731723	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
135449	141058	7755903663661447	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
135927	141749	7755903668446265	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
136547	142634	7755903674646235	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
137163	161562	7755903680806719	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
137782	162512	7755903686997398	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
138236	163463	7755903691524636	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
138861	165658	7755903697782379	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
139517	169815	7755903704333436	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
140263	191118	7755903711803378	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
141059	229281	7755903719756598	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
141750	229282	7755903726669730	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
163463	229409	7755903943801999	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
161562	229650	7755903924789765	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
162512	229691	7755903934289882	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
165658	229732	7755903965744524	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
142634	229788	7755903735514863	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
169816	230466	7755904007327849	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
191118	230871	7755904220357868	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
229789	232192	7755904630904355	zlib/libzlib.dll	c70d7c7719fed2bf
229789	232192	7755904630904355	zlib/libzlib.dll.a	c70d7c7719fed2bf
232196	247231	7755904631140378	zlib/minigzip.exe	b30485a50c6b3a85
232200	247812	7755904631170394	zlib/example64.exe	4a92267bf6f9e4ab
232204	248241	7755904631211393	zlib/minigzip64.exe	4f10a1aa1eb3582e
232192	248288	7755904631099551	zlib/example.exe	61852f41d57c7507
134931	254738	7755904853280767	OrderManager_autogen/timestamp	21ad19f5d651830a
134931	254738	7755904853280767	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
134931	254738	7755904853280767	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
134931	254738	7755904853280767	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
255896	308600	7755904868125722	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
257759	311970	7755904886761020	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
257286	317523	7755904882030474	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
256750	318447	7755904876676665	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
254924	319402	7755904858400171	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
258445	323771	7755904893626960	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
256311	327142	7755904872284316	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
254738	331721	7755904856555854	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
255529	338360	7755904864463259	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
255205	342001	7755904861228597	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
308600	396669	7755905395170067	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
311971	409257	7755905428870377	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
317523	409478	7755905484398522	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
319403	410405	7755905503197618	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
338360	410537	7755905692753690	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
318447	410938	7755905493641258	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
327142	410963	7755905580581724	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
331721	411277	7755905626372368	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
323771	411828	7755905546884591	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
342001	418880	7755905729180858	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
396670	419341	7755906275864244	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
419342	425688	7755906502585978	OrderManager.exe	130079037154008e
29	13376	7755919084386680	OrderManager_autogen/timestamp	21ad19f5d651830a
29	13376	7755919084386680	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
29	13376	7755919084386680	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
29	13376	7755919084386680	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
16340	63198	7755919119311421	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
15654	64448	7755919112459533	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
17053	64781	7755919126438435	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
13714	70812	7755919093053516	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
13380	73240	7755919089715805	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
14899	75187	7755919104908908	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
14309	82271	7755919099003089	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
82271	88604	7755919778618071	OrderManager.exe	130079037154008e
69	20585	7755924407274245	OrderManager_autogen/timestamp	21ad19f5d651830a
69	20585	7755924407274245	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
69	20585	7755924407274245	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
69	20585	7755924407274245	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
22191	44806	7755924427110877	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
22645	46145	7755924431643140	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
21762	47335	7755924422811195	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
20830	50660	7755924413485021	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
20588	53623	7755924411073369	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
21432	56082	7755924419512329	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
21146	61503	7755924416651401	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
61504	67785	7755924820228681	OrderManager.exe	130079037154008e
90	3024	7755927971870977	OrderManager_autogen/timestamp	21ad19f5d651830a
90	3024	7755927971870977	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
90	3024	7755927971870977	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
90	3024	7755927971870977	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
3027	22228	7755928001228526	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
22228	38367	7755928193243567	OrderManager.exe	130079037154008e
39	1880	7755933268022281	OrderManager_autogen/timestamp	21ad19f5d651830a
39	1880	7755933268022281	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
39	1880	7755933268022281	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
39	1880	7755933268022281	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1883	10923	7755933286464423	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
10923	18918	7755933376859539	OrderManager.exe	130079037154008e
15	194	7755912170096063	CMakeFiles/clean.additional	baedbe210acaa455
194	329	7755912171879264	clean	577b940aee94fbd7
15	198	7755912189693442	CMakeFiles/clean.additional	baedbe210acaa455
198	368	7755912191527827	clean	577b940aee94fbd7
22	1808	7755912199725042	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
176	1986	7755912201255942	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
408	2192	7755912203586466	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
573	2368	7755912205229594	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
751	2547	7755912207006472	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
914	2734	7755912208640242	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
1081	2736	7755912210315597	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
1273	2736	7755912212244453	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
1448	2737	7755912213991173	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
1633	2737	7755912215832707	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
1809	2738	7755912217587022	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
1986	2738	7755912219365432	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
2192	2903	7755912221422413	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
2368	3035	7755912223177573	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
2547	3283	7755912224978395	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
2734	3630	7755912235762867	zlib/zlib1rc.obj	5625031d43e5c7be
2734	3630	7755912235762867	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
3630	5286	7755912235799879	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
3810	5476	7755912237609592	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
3980	5644	7755912239305007	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
3283	5826	7755912232312286	zlib/libzlibstatic.a	22b16c9e053b901
4155	5829	7755912241047152	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
4351	6052	7755912243013845	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
4550	6278	7755912245006026	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
4723	6508	7755912246729408	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
4929	6753	7755912248795098	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
5106	7018	7755912250559890	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
5286	7406	7755912252312980	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
5477	8037	7755912254265981	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
5644	8038	7755912255944278	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
5830	8039	7755912257798611	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
6052	8040	7755912260015638	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
6278	8040	7755912262286271	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
6508	8043	7755912264585810	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
6753	8387	7755912267027806	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
7018	9182	7755912269683954	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
7406	10052	7755912273571052	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
8040	10884	7755912308174328	zlib/libzlib.dll	c70d7c7719fed2bf
8040	10884	7755912308174328	zlib/libzlib.dll.a	c70d7c7719fed2bf
10885	27703	7755912308356948	zlib/example.exe	61852f41d57c7507
10888	27735	7755912308391746	zlib/minigzip.exe	b30485a50c6b3a85
10892	27835	7755912308423854	zlib/example64.exe	4a92267bf6f9e4ab
10897	28445	7755912308473279	zlib/minigzip64.exe	4f10a1aa1eb3582e
5826	41406	7755912610182060	OrderManager_autogen/timestamp	21ad19f5d651830a
5826	41406	7755912610182060	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
5826	41406	7755912610182060	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
5826	41406	7755912610182060	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
42586	57732	7755912625364463	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
44484	65056	7755912644342467	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
43469	68120	7755912634202010	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
43949	71250	7755912638989712	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
45283	74475	7755912652342072	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
42966	83186	7755912629163565	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
41625	86206	7755912615758877	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
41407	91653	7755912613569779	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
57733	94672	7755912776828851	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
65057	98585	7755912850069590	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
42230	100302	7755912621805036	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
71251	106372	7755912912015927	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
************	7755912880711344	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
************	7755912944268880	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
************	7755913031377025	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
************	7755913061574195	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
************	7755912618682440	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
************	7755913116049581	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
************	7755913146227734	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
************	7755913185358774	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
100303	119922	7755913202538451	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
119922	126046	7755913398722943	OrderManager.exe	130079037154008e
