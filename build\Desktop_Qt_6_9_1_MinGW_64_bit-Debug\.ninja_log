# ninja log v6
5437	6899	****************	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
21	1738	****************	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
147	1901	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
4121	5643	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
5433	35756	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
1054	2545	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
1256	2546	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
9209	20633	****************	zlib/example64.exe	4a92267bf6f9e4ab
399	2077	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
13	154	****************	CMakeFiles/clean.additional	baedbe210acaa455
577	2235	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
5433	35756	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
742	2388	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
9204	20694	****************	zlib/minigzip.exe	b30485a50c6b3a85
896	2543	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
2543	3391	7755914858769213	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
9214	21027	7755914917028900	zlib/minigzip64.exe	4f10a1aa1eb3582e
1421	2546	7755914839093536	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
9200	20989	7755914916891888	zlib/example.exe	61852f41d57c7507
1582	2547	7755914840715858	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
1739	2547	7755914842260191	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
1901	2548	7755914843894688	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
2078	2731	7755914845668184	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
3805	5267	7755914862937090	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
4439	6027	7755914869282452	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
2235	2832	7755914847243162	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
2388	3059	7755914848770826	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
2543	3391	7755914858769213	zlib/zlib1rc.obj	5625031d43e5c7be
3392	4932	7755914858809620	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
3635	5112	7755914861248096	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
4268	5829	7755914867562709	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
3961	5436	7755914864494116	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
3059	5433	7755914855480818	zlib/libzlibstatic.a	22b16c9e053b901
4614	6236	7755914871031188	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
4767	6444	7755914872557489	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
4932	6660	7755914874203982	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
5113	6897	7755914876017799	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
5267	6898	7755914877559432	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
5433	35756	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
35756	75386	7755915182453944	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
5643	6899	7755914881320775	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
5829	6900	7755914883176507	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
6028	6903	7755914885163245	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
35929	69889	7755915184173195	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
6236	7153	7755914887249612	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
6444	7620	7755914889326563	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
6660	8308	7755914891489235	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
6900	9200	7755914916690973	zlib/libzlib.dll	c70d7c7719fed2bf
6900	9200	7755914916690973	zlib/libzlib.dll.a	c70d7c7719fed2bf
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
5433	35756	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
98868	104896	7755915813564338	OrderManager.exe	130079037154008e
15	3357	7755841096121232	build.ninja	497fbd042d0239fc
138	268	7755909368534215	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
69890	90638	7755915523790595	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
75386	93334	7755915578759148	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
76082	93500	7755915585712070	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
36178	92877	7755915186675739	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
36766	58512	7755915192547838	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
37564	59338	7755915200529152	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
37132	76082	7755915196198904	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
57520	73612	7755915400096166	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
58513	77008	7755915410013166	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
59339	79031	7755915418281230	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
38397	57520	7755915208865452	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
39242	60595	7755915217315768	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
36474	84679	7755915189630832	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
60007	88446	7755915424965800	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
73613	92368	7755915561014397	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
37976	60007	7755915204658322	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
60596	90652	7755915430852698	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
79032	98868	7755915615206346	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
77009	96903	7755915594979669	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
97	2736	7755918935238829	OrderManager_autogen/timestamp	21ad19f5d651830a
97	2736	7755918935238829	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
97	2736	7755918935238829	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
97	2736	7755918935238829	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
3121	24229	7755918965469360	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
2738	29408	7755918961633292	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
29408	36275	7755919228334120	OrderManager.exe	130079037154008e
84	2803	7755930907993456	OrderManager_autogen/timestamp	21ad19f5d651830a
84	2803	7755930907993456	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
84	2803	7755930907993456	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
84	2803	7755930907993456	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
3156	20170	7755930938717955	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
2805	23483	7755930935195113	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
23483	30213	7755931141975068	OrderManager.exe	130079037154008e
20	334	7755932498199472	CMakeFiles/clean.additional	baedbe210acaa455
30	396	7755932521337772	CMakeFiles/clean.additional	baedbe210acaa455
44	44493	7755932741006427	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
409	84842	7755932744653236	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
857	85801	7755932749125900	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
1393	89017	7755932754501856	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
2007	92358	7755932760630994	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
2635	127474	7755932766915936	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
3285	127476	7755932773421661	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
4309	127477	7755932783708949	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
7759	127478	7755932818161489	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
8675	127479	7755932827311147	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
85803	136818	7755933598606859	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
44494	137029	7755933185495175	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
84843	137077	7755933588982332	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
89017	137176	7755933630734707	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
92358	137783	7755933664142497	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
137784	138845	7755934118414895	zlib/libzlibstatic.a	22b16c9e053b901
23	941	7755934694979853	zlib/zlib1rc.obj	5625031d43e5c7be
23	941	7755934694979853	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
942	19193	7755934695030187	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
1082	27186	7755934696430423	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
1273	67731	7755934698344195	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
1519	74792	7755934700798207	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
1944	78125	7755934705046195	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
2571	90786	7755934711323745	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
3334	97305	7755934718955274	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
14017	99479	7755934825779191	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
16060	103711	7755934846220379	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
19194	110546	7755934877550205	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
27186	150522	7755934957472654	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
67731	155593	7755935362924635	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
103711	156782	7755935722721267	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
97305	156951	7755935658661152	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
78125	157027	7755935466866451	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
74793	157160	7755935433542550	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
90787	157360	7755935593468865	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
99479	157493	7755935680397568	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
110546	158162	7755935791066361	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
157361	159327	7755936278725210	zlib/libzlib.dll	c70d7c7719fed2bf
157361	159327	7755936278725210	zlib/libzlib.dll.a	c70d7c7719fed2bf
159338	173449	7755936278988591	zlib/minigzip64.exe	4f10a1aa1eb3582e
159327	173565	7755936278882147	zlib/example.exe	61852f41d57c7507
159334	173755	7755936278948387	zlib/example64.exe	4a92267bf6f9e4ab
159331	173785	7755936278923217	zlib/minigzip.exe	b30485a50c6b3a85
25	180317	7755936485486487	OrderManager_autogen/timestamp	21ad19f5d651830a
25	180317	7755936485486487	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
25	180317	7755936485486487	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
25	180317	7755936485486487	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
182756	270987	7755936513169286	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
270987	367625	7755901395472037	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
367625	470049	7755902361851202	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
908	54110	7755904384583586	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
1648	60620	7755904391989219	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
3118	67359	7755904406687081	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
1241	69411	7755904387912288	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
178	73644	7755904377284978	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
24	77668	7755904375747113	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
636	80759	7755904381864647	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
2127	85288	7755904396782295	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
423	88390	7755904379735144	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
2589	90330	7755904401401438	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
67360	95518	7755905049107175	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
************	7755904981714736	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
************	7755905069621608	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
************	7755905152184462	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
************	7755904916616594	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
************	7755905111950771	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
************	7755905183108458	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
************	7755905228387535	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
110433	116701	7755905479835911	OrderManager.exe	130079037154008e
811	22667	7755916163693349	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
981	24307	7755916165400552	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
670	24435	7755916162285639	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
157	27765	7755916157147326	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
22	30129	7755916155811604	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
506	31204	7755916160636443	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
333	35251	7755916158907493	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
35252	41407	7755916508104218	OrderManager.exe	130079037154008e
155	29137	7755921653319617	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
752	29649	7755921659280073	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
23	29988	7755921651993277	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
579	30679	7755921657557390	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
401	35460	7755921655778490	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
35460	41448	7755922006367779	OrderManager.exe	130079037154008e
