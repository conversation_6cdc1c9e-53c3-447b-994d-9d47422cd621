/****************************************************************************
** Meta object code from reading C++ file 'orderapi.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../legacy/api/orderapi.h"
#include <QtNetwork/QSslError>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'orderapi.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN8OrderAPIE_t {};
} // unnamed namespace

template <> constexpr inline auto OrderAPI::qt_create_metaobjectdata<qt_meta_tag_ZN8OrderAPIE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "OrderAPI",
        "loginResult",
        "",
        "success",
        "message",
        "token",
        "userId",
        "uid",
        "orderRefreshResult",
        "orders",
        "recordCount",
        "orderAcceptResult",
        "userInfoResult",
        "userInfo",
        "networkError",
        "error",
        "debugLog",
        "msg",
        "focusUserResult",
        "focusUserId",
        "cancelFocusUserResult",
        "addBlackResult",
        "removeBlackResult",
        "fastOrderFound",
        "orderObj",
        "takeOrder",
        "serialNo",
        "stamp",
        "payPass",
        "loginId",
        "proxyHost",
        "proxyPort",
        "proxyUser",
        "proxyPass",
        "onNetworkReplyFinished",
        "reportAcceptResult",
        "logPerformance",
        "processAcceptOrder",
        "setNetworkEngine",
        "NetworkEngine",
        "engine",
        "getNetworkEngine",
        "executeNetworkRequest",
        "url",
        "postData",
        "headers",
        "getPerformanceStats",
        "NetworkPerformanceStats",
        "resetPerformanceStats",
        "getPerformanceReport",
        "getCurrentFingerprint",
        "TLSFingerprintInfo",
        "verifyQuarkFingerprint",
        "getFingerprintReport",
        "generateUltraFastTLSDebugReport",
        "testUltraFastTLS"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'loginResult'
        QtMocHelpers::SignalData<void(bool, const QString &, const QString &, const QString &, const QString &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 }, { QMetaType::QString, 4 }, { QMetaType::QString, 5 }, { QMetaType::QString, 6 },
            { QMetaType::QString, 7 },
        }}),
        // Signal 'orderRefreshResult'
        QtMocHelpers::SignalData<void(bool, const QString &, const QJsonArray &, int)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 }, { QMetaType::QString, 4 }, { QMetaType::QJsonArray, 9 }, { QMetaType::Int, 10 },
        }}),
        // Signal 'orderAcceptResult'
        QtMocHelpers::SignalData<void(bool, const QString &)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 }, { QMetaType::QString, 4 },
        }}),
        // Signal 'userInfoResult'
        QtMocHelpers::SignalData<void(bool, const QString &, const QJsonObject &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 }, { QMetaType::QString, 4 }, { QMetaType::QJsonObject, 13 },
        }}),
        // Signal 'networkError'
        QtMocHelpers::SignalData<void(const QString &)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 15 },
        }}),
        // Signal 'debugLog'
        QtMocHelpers::SignalData<void(const QString &)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 17 },
        }}),
        // Signal 'focusUserResult'
        QtMocHelpers::SignalData<void(bool, const QString &, const QString &)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 }, { QMetaType::QString, 4 }, { QMetaType::QString, 19 },
        }}),
        // Signal 'cancelFocusUserResult'
        QtMocHelpers::SignalData<void(bool, const QString &, const QString &)>(20, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 }, { QMetaType::QString, 4 }, { QMetaType::QString, 19 },
        }}),
        // Signal 'addBlackResult'
        QtMocHelpers::SignalData<void(bool, const QString &, const QString &)>(21, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 }, { QMetaType::QString, 4 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'removeBlackResult'
        QtMocHelpers::SignalData<void(bool, const QString &, const QString &)>(22, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 }, { QMetaType::QString, 4 }, { QMetaType::QString, 6 },
        }}),
        // Signal 'fastOrderFound'
        QtMocHelpers::SignalData<void(const QJsonObject &)>(23, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonObject, 24 },
        }}),
        // Signal 'takeOrder'
        QtMocHelpers::SignalData<void(const QString &, const QString &, const QString &, const QString &, const QString &, const QString &, int, const QString &, const QString &)>(25, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 26 }, { QMetaType::QString, 27 }, { QMetaType::QString, 6 }, { QMetaType::QString, 28 },
            { QMetaType::QString, 29 }, { QMetaType::QString, 30 }, { QMetaType::Int, 31 }, { QMetaType::QString, 32 },
            { QMetaType::QString, 33 },
        }}),
        // Slot 'onNetworkReplyFinished'
        QtMocHelpers::SlotData<void()>(34, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'reportAcceptResult'
        QtMocHelpers::SlotData<void(bool, const QString &, const QString &)>(35, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 }, { QMetaType::QString, 4 }, { QMetaType::QString, 26 },
        }}),
        // Slot 'logPerformance'
        QtMocHelpers::SlotData<void(const QString &)>(36, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 4 },
        }}),
        // Slot 'processAcceptOrder'
        QtMocHelpers::SlotData<void(const QString &, const QString &, const QString &, const QString &, const QString &, const QString &, int, const QString &, const QString &)>(37, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 26 }, { QMetaType::QString, 27 }, { QMetaType::QString, 6 }, { QMetaType::QString, 28 },
            { QMetaType::QString, 29 }, { QMetaType::QString, 30 }, { QMetaType::Int, 31 }, { QMetaType::QString, 32 },
            { QMetaType::QString, 33 },
        }}),
        // Slot 'setNetworkEngine'
        QtMocHelpers::SlotData<void(NetworkEngine)>(38, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 39, 40 },
        }}),
        // Slot 'getNetworkEngine'
        QtMocHelpers::SlotData<NetworkEngine() const>(41, 2, QMC::AccessPublic, 0x80000000 | 39),
        // Slot 'executeNetworkRequest'
        QtMocHelpers::SlotData<QString(const QString &, const QString &, const QString &)>(42, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::QString, 43 }, { QMetaType::QString, 44 }, { QMetaType::QString, 45 },
        }}),
        // Slot 'executeNetworkRequest'
        QtMocHelpers::SlotData<QString(const QString &, const QString &)>(42, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::QString, {{
            { QMetaType::QString, 43 }, { QMetaType::QString, 44 },
        }}),
        // Slot 'getPerformanceStats'
        QtMocHelpers::SlotData<NetworkPerformanceStats() const>(46, 2, QMC::AccessPublic, 0x80000000 | 47),
        // Slot 'resetPerformanceStats'
        QtMocHelpers::SlotData<void()>(48, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'getPerformanceReport'
        QtMocHelpers::SlotData<QString() const>(49, 2, QMC::AccessPublic, QMetaType::QString),
        // Slot 'getCurrentFingerprint'
        QtMocHelpers::SlotData<TLSFingerprintInfo() const>(50, 2, QMC::AccessPublic, 0x80000000 | 51),
        // Slot 'verifyQuarkFingerprint'
        QtMocHelpers::SlotData<bool() const>(52, 2, QMC::AccessPublic, QMetaType::Bool),
        // Slot 'getFingerprintReport'
        QtMocHelpers::SlotData<QString() const>(53, 2, QMC::AccessPublic, QMetaType::QString),
        // Slot 'generateUltraFastTLSDebugReport'
        QtMocHelpers::SlotData<QString()>(54, 2, QMC::AccessPublic, QMetaType::QString),
        // Slot 'testUltraFastTLS'
        QtMocHelpers::SlotData<void()>(55, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<OrderAPI, qt_meta_tag_ZN8OrderAPIE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject OrderAPI::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN8OrderAPIE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN8OrderAPIE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN8OrderAPIE_t>.metaTypes,
    nullptr
} };

void OrderAPI::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<OrderAPI *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->loginResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[4])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[5]))); break;
        case 1: _t->orderRefreshResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QJsonArray>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[4]))); break;
        case 2: _t->orderAcceptResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 3: _t->userInfoResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[3]))); break;
        case 4: _t->networkError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->debugLog((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->focusUserResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 7: _t->cancelFocusUserResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 8: _t->addBlackResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 9: _t->removeBlackResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 10: _t->fastOrderFound((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 11: _t->takeOrder((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[4])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[5])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[6])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[7])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[8])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[9]))); break;
        case 12: _t->onNetworkReplyFinished(); break;
        case 13: _t->reportAcceptResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 14: _t->logPerformance((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 15: _t->processAcceptOrder((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[4])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[5])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[6])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[7])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[8])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[9]))); break;
        case 16: _t->setNetworkEngine((*reinterpret_cast< std::add_pointer_t<NetworkEngine>>(_a[1]))); break;
        case 17: { NetworkEngine _r = _t->getNetworkEngine();
            if (_a[0]) *reinterpret_cast< NetworkEngine*>(_a[0]) = std::move(_r); }  break;
        case 18: { QString _r = _t->executeNetworkRequest((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 19: { QString _r = _t->executeNetworkRequest((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 20: { NetworkPerformanceStats _r = _t->getPerformanceStats();
            if (_a[0]) *reinterpret_cast< NetworkPerformanceStats*>(_a[0]) = std::move(_r); }  break;
        case 21: _t->resetPerformanceStats(); break;
        case 22: { QString _r = _t->getPerformanceReport();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 23: { TLSFingerprintInfo _r = _t->getCurrentFingerprint();
            if (_a[0]) *reinterpret_cast< TLSFingerprintInfo*>(_a[0]) = std::move(_r); }  break;
        case 24: { bool _r = _t->verifyQuarkFingerprint();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 25: { QString _r = _t->getFingerprintReport();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 26: { QString _r = _t->generateUltraFastTLSDebugReport();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 27: _t->testUltraFastTLS(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(bool , const QString & , const QString & , const QString & , const QString & )>(_a, &OrderAPI::loginResult, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(bool , const QString & , const QJsonArray & , int )>(_a, &OrderAPI::orderRefreshResult, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(bool , const QString & )>(_a, &OrderAPI::orderAcceptResult, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(bool , const QString & , const QJsonObject & )>(_a, &OrderAPI::userInfoResult, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(const QString & )>(_a, &OrderAPI::networkError, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(const QString & )>(_a, &OrderAPI::debugLog, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(bool , const QString & , const QString & )>(_a, &OrderAPI::focusUserResult, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(bool , const QString & , const QString & )>(_a, &OrderAPI::cancelFocusUserResult, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(bool , const QString & , const QString & )>(_a, &OrderAPI::addBlackResult, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(bool , const QString & , const QString & )>(_a, &OrderAPI::removeBlackResult, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(const QJsonObject & )>(_a, &OrderAPI::fastOrderFound, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderAPI::*)(const QString & , const QString & , const QString & , const QString & , const QString & , const QString & , int , const QString & , const QString & )>(_a, &OrderAPI::takeOrder, 11))
            return;
    }
}

const QMetaObject *OrderAPI::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *OrderAPI::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN8OrderAPIE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int OrderAPI::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 28)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 28;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 28)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 28;
    }
    return _id;
}

// SIGNAL 0
void OrderAPI::loginResult(bool _t1, const QString & _t2, const QString & _t3, const QString & _t4, const QString & _t5)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2, _t3, _t4, _t5);
}

// SIGNAL 1
void OrderAPI::orderRefreshResult(bool _t1, const QString & _t2, const QJsonArray & _t3, int _t4)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2, _t3, _t4);
}

// SIGNAL 2
void OrderAPI::orderAcceptResult(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2);
}

// SIGNAL 3
void OrderAPI::userInfoResult(bool _t1, const QString & _t2, const QJsonObject & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2, _t3);
}

// SIGNAL 4
void OrderAPI::networkError(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void OrderAPI::debugLog(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}

// SIGNAL 6
void OrderAPI::focusUserResult(bool _t1, const QString & _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1, _t2, _t3);
}

// SIGNAL 7
void OrderAPI::cancelFocusUserResult(bool _t1, const QString & _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1, _t2, _t3);
}

// SIGNAL 8
void OrderAPI::addBlackResult(bool _t1, const QString & _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 8, nullptr, _t1, _t2, _t3);
}

// SIGNAL 9
void OrderAPI::removeBlackResult(bool _t1, const QString & _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 9, nullptr, _t1, _t2, _t3);
}

// SIGNAL 10
void OrderAPI::fastOrderFound(const QJsonObject & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 10, nullptr, _t1);
}

// SIGNAL 11
void OrderAPI::takeOrder(const QString & _t1, const QString & _t2, const QString & _t3, const QString & _t4, const QString & _t5, const QString & _t6, int _t7, const QString & _t8, const QString & _t9)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 11, nullptr, _t1, _t2, _t3, _t4, _t5, _t6, _t7, _t8, _t9);
}
QT_WARNING_POP
