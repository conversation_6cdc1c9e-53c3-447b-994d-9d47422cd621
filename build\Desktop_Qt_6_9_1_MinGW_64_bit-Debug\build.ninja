# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: OrderManager
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/
# =============================================================================
# Object build statements for EXECUTABLE target OrderManager


#############################################
# Order-only phony target for OrderManager

build cmake_object_order_depends_target_OrderManager: phony || OrderManager_autogen OrderManager_autogen/mocs_compilation.cpp OrderManager_autogen/timestamp OrderManager_autogen_timestamp_deps cmake_object_order_depends_target_zlibstatic

build CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\OrderManager_autogen\mocs_compilation.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\OrderManager_autogen

build CMakeFiles/OrderManager.dir/main.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/main.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\main.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir

build CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/src/ui/mainwindow.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\src\ui\mainwindow.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\src\ui

build CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/api/orderapi.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\api\orderapi.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\api

build CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/workers/filterworker.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\workers\filterworker.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\workers

build CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/network/ultrafasttls.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\network\ultrafasttls.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\network

build CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/network/ultrafasttls_debug_monitor.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\network\ultrafasttls_debug_monitor.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\network

build CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/network/network_client.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\network\network_client.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\network

build CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/network/request_builder.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\network\request_builder.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\network

build CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/network/response_processor.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\network\response_processor.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\network

build CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/utils/simple_logger.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\utils\simple_logger.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\utils

build CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/utils/error_handler.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\utils\error_handler.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\utils

build CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/utils/json_parser.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\utils\json_parser.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\utils

build CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/services/encryption_service.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\services\encryption_service.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\services

build CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/services/authentication_service.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\services\authentication_service.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\services

build CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/services/service_container.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\services\service_container.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\services

build CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/legacy/services/business_logic.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\legacy\services\business_logic.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\legacy\services

build CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/src/config/app_config.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\src\config\app_config.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\src\config

build CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/src/core/utils/logger.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\src\core\utils\logger.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\src\core\utils

build CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/src/network/engines/ultrafasttls_adapter.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\src\network\engines\ultrafasttls_adapter.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\src\network\engines

build CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj: CXX_COMPILER__OrderManager_unscanned_Debug C$:/eee/cc/src/integration/legacy_api_adapter.cpp || cmake_object_order_depends_target_OrderManager
  DEFINES = -DMINGW_HAS_SECURE_API=1 -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN32_LEAN_AND_MEAN -DWIN64 -D_CRT_SECURE_NO_WARNINGS -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_HAS_ITERATOR_DEBUGGING=0 -D_SECURE_SCL=0 -D_UNICODE -D_WIN64
  DEP_FILE = CMakeFiles\OrderManager.dir\src\integration\legacy_api_adapter.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -fdiagnostics-color=always -Wall -Wextra -Wpedantic
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/include -I"C:/Program Files/OpenSSL-Win64/include" -IC:/eee/cc -IC:/eee/cc/src -IC:/eee/cc/src/core -IC:/eee/cc/src/core/utils -IC:/eee/cc/src/core/models -IC:/eee/cc/src/core/services -IC:/eee/cc/src/core/controllers -IC:/eee/cc/src/network -IC:/eee/cc/src/network/engines -IC:/eee/cc/src/integration -IC:/eee/cc/src/config -IC:/eee/cc/src/ui -IC:/eee/cc/legacy -IC:/eee/cc/legacy/api -IC:/eee/cc/legacy/network -IC:/eee/cc/legacy/services -IC:/eee/cc/legacy/utils -IC:/eee/cc/legacy/workers -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/Libraries/zlib131/zlib-1.3.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtWidgets -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  OBJECT_FILE_DIR = CMakeFiles\OrderManager.dir\src\integration


# =============================================================================
# Link build statements for EXECUTABLE target OrderManager


#############################################
# Link the executable OrderManager.exe

build OrderManager.exe: CXX_EXECUTABLE_LINKER__OrderManager_Debug CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj CMakeFiles/OrderManager.dir/main.cpp.obj CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj | C$:/Qt/6.9.1/mingw_64/lib/libQt6Widgets.a C$:/Qt/6.9.1/mingw_64/lib/libQt6Network.a C$:/Qt/6.9.1/mingw_64/lib/libQt6Concurrent.a zlib/libzlibstatic.a C$:/Program$ Files/OpenSSL-Win64/lib/VC/x64/MD/libssl.lib C$:/Program$ Files/OpenSSL-Win64/lib/VC/x64/MD/libcrypto.lib C$:/Qt/6.9.1/mingw_64/lib/libQt6Gui.a C$:/Qt/6.9.1/mingw_64/lib/libQt6Core.a C$:/Qt/6.9.1/mingw_64/lib/libQt6EntryPoint.a || OrderManager_autogen OrderManager_autogen_timestamp_deps zlib/libzlibstatic.a
  FLAGS = -DQT_QML_DEBUG -g
  LINK_FLAGS = -mwindows
  LINK_LIBRARIES = C:/Qt/6.9.1/mingw_64/lib/libQt6Widgets.a  C:/Qt/6.9.1/mingw_64/lib/libQt6Network.a  C:/Qt/6.9.1/mingw_64/lib/libQt6Concurrent.a  -lws2_32  -lcrypt32  zlib/libzlibstatic.a  "C:/Program Files/OpenSSL-Win64/lib/VC/x64/MD/libssl.lib"  "C:/Program Files/OpenSSL-Win64/lib/VC/x64/MD/libcrypto.lib"  C:/Qt/6.9.1/mingw_64/lib/libQt6Gui.a  -ld3d11  -ldxgi  -ldxguid  -ld3d12  C:/Qt/6.9.1/mingw_64/lib/libQt6Core.a  -lmpr  -luserenv  -lmingw32  C:/Qt/6.9.1/mingw_64/lib/libQt6EntryPoint.a  -lshell32  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\OrderManager.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = OrderManager.exe
  TARGET_IMPLIB = libOrderManager.dll.a
  TARGET_PDB = OrderManager.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug && C:\Qt\Tools\CMake_64\bin\cmake-gui.exe -SC:\eee\cc -BC:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe --regenerate-during-build -SC:\eee\cc -BC:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util


#############################################
# Utility command for OrderManager_autogen_timestamp_deps

build OrderManager_autogen_timestamp_deps: phony zlib/libzlibstatic.a


#############################################
# Utility command for OrderManager_autogen

build OrderManager_autogen: phony CMakeFiles/OrderManager_autogen OrderManager_autogen/include/src/ui/ui_mainwindow.h OrderManager_autogen/timestamp OrderManager_autogen/mocs_compilation.cpp OrderManager_autogen_timestamp_deps


#############################################
# Custom command for OrderManager_autogen\timestamp

build OrderManager_autogen/timestamp OrderManager_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}OrderManager_autogen/timestamp ${cmake_ninja_workdir}OrderManager_autogen/mocs_compilation.cpp: CUSTOM_COMMAND C$:/Qt/6.9.1/mingw_64/bin/moc.exe C$:/Qt/6.9.1/mingw_64/bin/uic.exe || OrderManager_autogen_timestamp_deps zlib/libzlibstatic.a
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/CMakeFiles/OrderManager_autogen.dir/AutogenInfo.json Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe -E touch C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp && C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_transform_depfile Ninja gccdepfile C:/eee/cc C:/eee/cc C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/deps C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/CMakeFiles/d/2b0277d5abceb98e3fa0de857cd2e0af5e27f5d97cf40c98b7281d78fb3353dd.d"
  DESC = Automatic MOC and UIC for target OrderManager
  depfile = CMakeFiles\d\2b0277d5abceb98e3fa0de857cd2e0af5e27f5d97cf40c98b7281d78fb3353dd.d
  deps = gcc
  restat = 1


#############################################
# Phony custom command for CMakeFiles\OrderManager_autogen

build CMakeFiles/OrderManager_autogen OrderManager_autogen/include/src/ui/ui_mainwindow.h | ${cmake_ninja_workdir}CMakeFiles/OrderManager_autogen ${cmake_ninja_workdir}OrderManager_autogen/include/src/ui/ui_mainwindow.h: phony OrderManager_autogen/timestamp || OrderManager_autogen_timestamp_deps zlib/libzlibstatic.a

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/eee/cc/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target zlib


#############################################
# Order-only phony target for zlib

build cmake_object_order_depends_target_zlib: phony || zlib/zlib1rc.obj

build zlib/CMakeFiles/zlib.dir/adler32.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/adler32.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\adler32.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/compress.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/compress.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\compress.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/crc32.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/crc32.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\crc32.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/deflate.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/deflate.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\deflate.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/gzclose.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/gzclose.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\gzclose.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/gzlib.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/gzlib.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\gzlib.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/gzread.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/gzread.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\gzread.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/gzwrite.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/gzwrite.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\gzwrite.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/inflate.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/inflate.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\inflate.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/infback.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/infback.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\infback.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/inftrees.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/inftrees.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\inftrees.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/inffast.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/inffast.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\inffast.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/trees.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/trees.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\trees.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/uncompr.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/uncompr.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\uncompr.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir

build zlib/CMakeFiles/zlib.dir/zutil.c.obj: C_COMPILER__zlib_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/zutil.c || cmake_object_order_depends_target_zlib
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -DZLIB_DLL -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlib.dir\zutil.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlib.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target zlib


#############################################
# Link the shared library zlib\libzlib.dll

build zlib/libzlib.dll zlib/libzlib.dll.a: C_SHARED_LIBRARY_LINKER__zlib_Debug zlib/zlib1rc.obj zlib/CMakeFiles/zlib.dir/adler32.c.obj zlib/CMakeFiles/zlib.dir/compress.c.obj zlib/CMakeFiles/zlib.dir/crc32.c.obj zlib/CMakeFiles/zlib.dir/deflate.c.obj zlib/CMakeFiles/zlib.dir/gzclose.c.obj zlib/CMakeFiles/zlib.dir/gzlib.c.obj zlib/CMakeFiles/zlib.dir/gzread.c.obj zlib/CMakeFiles/zlib.dir/gzwrite.c.obj zlib/CMakeFiles/zlib.dir/inflate.c.obj zlib/CMakeFiles/zlib.dir/infback.c.obj zlib/CMakeFiles/zlib.dir/inftrees.c.obj zlib/CMakeFiles/zlib.dir/inffast.c.obj zlib/CMakeFiles/zlib.dir/trees.c.obj zlib/CMakeFiles/zlib.dir/uncompr.c.obj zlib/CMakeFiles/zlib.dir/zutil.c.obj
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_LIBRARIES = -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = zlib\CMakeFiles\zlib.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  RESTAT = 1
  TARGET_FILE = zlib\libzlib.dll
  TARGET_IMPLIB = zlib\libzlib.dll.a
  TARGET_PDB = zlib.dll.dbg

# =============================================================================
# Object build statements for STATIC_LIBRARY target zlibstatic


#############################################
# Order-only phony target for zlibstatic

build cmake_object_order_depends_target_zlibstatic: phony || .

build zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/adler32.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\adler32.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/compress.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/compress.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\compress.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/crc32.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\crc32.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/deflate.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\deflate.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/gzclose.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\gzclose.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/gzlib.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\gzlib.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/gzread.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\gzread.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/gzwrite.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\gzwrite.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/inflate.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\inflate.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/infback.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/infback.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\infback.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/inftrees.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\inftrees.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/inffast.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\inffast.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/trees.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/trees.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\trees.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/uncompr.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\uncompr.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir

build zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj: C_COMPILER__zlibstatic_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/zutil.c || cmake_object_order_depends_target_zlibstatic
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\zlibstatic.dir\zutil.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\zlibstatic.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target zlibstatic


#############################################
# Link the static library zlib\libzlibstatic.a

build zlib/libzlibstatic.a: C_STATIC_LIBRARY_LINKER__zlibstatic_Debug zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj zlib/CMakeFiles/zlibstatic.dir/compress.c.obj zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj zlib/CMakeFiles/zlibstatic.dir/infback.c.obj zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj zlib/CMakeFiles/zlibstatic.dir/trees.c.obj zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = zlib\CMakeFiles\zlibstatic.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = zlib\libzlibstatic.a
  TARGET_PDB = zlibstatic.a.dbg

# =============================================================================
# Object build statements for EXECUTABLE target example


#############################################
# Order-only phony target for example

build cmake_object_order_depends_target_example: phony || cmake_object_order_depends_target_zlib

build zlib/CMakeFiles/example.dir/test/example.c.obj: C_COMPILER__example_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/test/example.c || cmake_object_order_depends_target_example
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\example.dir\test\example.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\example.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\example.dir\test


# =============================================================================
# Link build statements for EXECUTABLE target example


#############################################
# Link the executable zlib\example.exe

build zlib/example.exe: C_EXECUTABLE_LINKER__example_Debug zlib/CMakeFiles/example.dir/test/example.c.obj | zlib/libzlib.dll.a || zlib/libzlib.dll
  FLAGS = -g
  LINK_LIBRARIES = zlib/libzlib.dll.a  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = zlib\CMakeFiles\example.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = zlib\example.exe
  TARGET_IMPLIB = zlib\libexample.dll.a
  TARGET_PDB = example.exe.dbg

# =============================================================================
# Object build statements for EXECUTABLE target minigzip


#############################################
# Order-only phony target for minigzip

build cmake_object_order_depends_target_minigzip: phony || cmake_object_order_depends_target_zlib

build zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj: C_COMPILER__minigzip_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/test/minigzip.c || cmake_object_order_depends_target_minigzip
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\minigzip.dir\test\minigzip.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\minigzip.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\minigzip.dir\test


# =============================================================================
# Link build statements for EXECUTABLE target minigzip


#############################################
# Link the executable zlib\minigzip.exe

build zlib/minigzip.exe: C_EXECUTABLE_LINKER__minigzip_Debug zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj | zlib/libzlib.dll.a || zlib/libzlib.dll
  FLAGS = -g
  LINK_LIBRARIES = zlib/libzlib.dll.a  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = zlib\CMakeFiles\minigzip.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = zlib\minigzip.exe
  TARGET_IMPLIB = zlib\libminigzip.dll.a
  TARGET_PDB = minigzip.exe.dbg

# =============================================================================
# Object build statements for EXECUTABLE target example64


#############################################
# Order-only phony target for example64

build cmake_object_order_depends_target_example64: phony || cmake_object_order_depends_target_zlib

build zlib/CMakeFiles/example64.dir/test/example.c.obj: C_COMPILER__example64_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/test/example.c || cmake_object_order_depends_target_example64
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\example64.dir\test\example.c.obj.d
  FLAGS = -g -fdiagnostics-color=always -D_FILE_OFFSET_BITS=64
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\example64.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\example64.dir\test


# =============================================================================
# Link build statements for EXECUTABLE target example64


#############################################
# Link the executable zlib\example64.exe

build zlib/example64.exe: C_EXECUTABLE_LINKER__example64_Debug zlib/CMakeFiles/example64.dir/test/example.c.obj | zlib/libzlib.dll.a || zlib/libzlib.dll
  FLAGS = -g
  LINK_LIBRARIES = zlib/libzlib.dll.a  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = zlib\CMakeFiles\example64.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = zlib\example64.exe
  TARGET_IMPLIB = zlib\libexample64.dll.a
  TARGET_PDB = example64.exe.dbg

# =============================================================================
# Object build statements for EXECUTABLE target minigzip64


#############################################
# Order-only phony target for minigzip64

build cmake_object_order_depends_target_minigzip64: phony || cmake_object_order_depends_target_zlib

build zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj: C_COMPILER__minigzip64_unscanned_Debug C$:/Libraries/zlib131/zlib-1.3.1/test/minigzip.c || cmake_object_order_depends_target_minigzip64
  DEFINES = -DNDEBUG -DNOMINMAX -DOPENSSL_3_PLUS -DOPENSSL_FOUND -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_HAS_ITERATOR_DEBUGGING=0 -D_LARGEFILE64_SOURCE=1 -D_SECURE_SCL=0
  DEP_FILE = zlib\CMakeFiles\minigzip64.dir\test\minigzip.c.obj.d
  FLAGS = -g -fdiagnostics-color=always -D_FILE_OFFSET_BITS=64
  INCLUDES = -IC:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -IC:/eee/cc -IC:/Libraries/zlib131/zlib-1.3.1
  OBJECT_DIR = zlib\CMakeFiles\minigzip64.dir
  OBJECT_FILE_DIR = zlib\CMakeFiles\minigzip64.dir\test


# =============================================================================
# Link build statements for EXECUTABLE target minigzip64


#############################################
# Link the executable zlib\minigzip64.exe

build zlib/minigzip64.exe: C_EXECUTABLE_LINKER__minigzip64_Debug zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj | zlib/libzlib.dll.a || zlib/libzlib.dll
  FLAGS = -g
  LINK_LIBRARIES = zlib/libzlib.dll.a  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = zlib\CMakeFiles\minigzip64.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = zlib\minigzip64.exe
  TARGET_IMPLIB = zlib\libminigzip64.dll.a
  TARGET_PDB = minigzip64.exe.dbg


#############################################
# Utility command for edit_cache

build zlib/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\zlib && C:\Qt\Tools\CMake_64\bin\cmake-gui.exe -SC:\eee\cc -BC:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build zlib/edit_cache: phony zlib/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build zlib/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\zlib && C:\Qt\Tools\CMake_64\bin\cmake.exe --regenerate-during-build -SC:\eee\cc -BC:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build zlib/rebuild_cache: phony zlib/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build zlib/list_install_components: phony


#############################################
# Utility command for install

build zlib/CMakeFiles/install.util: CUSTOM_COMMAND zlib/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\zlib && C:\Qt\Tools\CMake_64\bin\cmake.exe -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build zlib/install: phony zlib/CMakeFiles/install.util


#############################################
# Utility command for install/local

build zlib/CMakeFiles/install/local.util: CUSTOM_COMMAND zlib/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\zlib && C:\Qt\Tools\CMake_64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build zlib/install/local: phony zlib/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build zlib/CMakeFiles/install/strip.util: CUSTOM_COMMAND zlib/all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\zlib && C:\Qt\Tools\CMake_64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build zlib/install/strip: phony zlib/CMakeFiles/install/strip.util


#############################################
# Custom command for zlib\zlib1rc.obj

build zlib/zlib1rc.obj | ${cmake_ninja_workdir}zlib/zlib1rc.obj: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\eee\cc\build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\zlib && C:\Qt\Tools\mingw1310_64\bin\windres.exe -D GCC_WINDRES -I C:/Libraries/zlib131/zlib-1.3.1 -I C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib -o C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj -i C:/Libraries/zlib131/zlib-1.3.1/win32/zlib1.rc"
  DESC = Generating zlib1rc.obj
  restat = 1

# =============================================================================
# Target aliases.

build OrderManager: phony OrderManager.exe

build example: phony zlib/example.exe

build example.exe: phony zlib/example.exe

build example64: phony zlib/example64.exe

build example64.exe: phony zlib/example64.exe

build libzlib.dll: phony zlib/libzlib.dll

build libzlibstatic.a: phony zlib/libzlibstatic.a

build minigzip: phony zlib/minigzip.exe

build minigzip.exe: phony zlib/minigzip.exe

build minigzip64: phony zlib/minigzip64.exe

build minigzip64.exe: phony zlib/minigzip64.exe

build zlib: phony zlib/libzlib.dll

build zlibstatic: phony zlib/libzlibstatic.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug

build all: phony OrderManager.exe zlib/all

# =============================================================================

#############################################
# Folder: C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib

build zlib/all: phony zlib/libzlib.dll zlib/libzlibstatic.a zlib/example.exe zlib/minigzip.exe zlib/example64.exe zlib/minigzip64.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc/package-manager/auto-setup.cmake .qtc/package-manager/maintenance_tool_provider.cmake C$:/Libraries/zlib131/zlib-1.3.1/CMakeLists.txt C$:/Libraries/zlib131/zlib-1.3.1/zconf.h.cmakein C$:/Libraries/zlib131/zlib-1.3.1/zlib.pc.cmakein C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfigExtras.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointMinGW32Target.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiPlugins.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QGifPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QGifPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QICOPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QICOPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QJpegPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QJpegPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkPlugins.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsVersionlessTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsPlugins.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/FindWrapAtomic.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/FindWrapVulkanHeaders.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Targets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtFeature.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtFeatureCommon.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtInstallPaths.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXCompilerFlag.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckFunctionExists.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckIncludeFile.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckIncludeFileCXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckLibraryExists.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckTypeSize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-C.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindOpenSSL.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindPackageMessage.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindVulkan.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindZLIB.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/GNUInstallDirs.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckCompilerFlag.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckFlagCommonConfig.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C-ABI.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-windres.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake C$:/eee/cc/CMakeLists.txt CMakeCache.txt CMakeFiles/3.30.5/CMakeCCompiler.cmake CMakeFiles/3.30.5/CMakeCXXCompiler.cmake CMakeFiles/3.30.5/CMakeRCCompiler.cmake CMakeFiles/3.30.5/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc/package-manager/auto-setup.cmake .qtc/package-manager/maintenance_tool_provider.cmake C$:/Libraries/zlib131/zlib-1.3.1/CMakeLists.txt C$:/Libraries/zlib131/zlib-1.3.1/zconf.h.cmakein C$:/Libraries/zlib131/zlib-1.3.1/zlib.pc.cmakein C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate/Qt6ConcurrentPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate/Qt6CorePrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfigExtras.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointMinGW32Target.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate/Qt6GuiPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiPlugins.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QGifPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QGifPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QICOPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QICOPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QJpegPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QJpegPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate/Qt6NetworkPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkPlugins.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsPrivate/Qt6WidgetsPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsVersionlessTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsPlugins.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-relwithdebinfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateAdditionalTargetInfo.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfig.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateVersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/FindWrapAtomic.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/FindWrapVulkanHeaders.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Targets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtFeature.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtFeatureCommon.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtInstallPaths.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake C$:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXCompilerFlag.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckFunctionExists.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckIncludeFile.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckIncludeFileCXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckLibraryExists.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckTypeSize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-C.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindOpenSSL.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindPackageMessage.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindVulkan.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindZLIB.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/GNUInstallDirs.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckCompilerFlag.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckFlagCommonConfig.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C-ABI.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-windres.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake C$:/eee/cc/CMakeLists.txt CMakeCache.txt CMakeFiles/3.30.5/CMakeCCompiler.cmake CMakeFiles/3.30.5/CMakeCXXCompiler.cmake CMakeFiles/3.30.5/CMakeRCCompiler.cmake CMakeFiles/3.30.5/CMakeSystem.cmake: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL
  CONFIG = Debug


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
