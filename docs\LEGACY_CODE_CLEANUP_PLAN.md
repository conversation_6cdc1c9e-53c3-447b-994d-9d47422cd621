# 🧹 沉淀代码清理计划

## 📊 沉淀代码分析

### ❌ 确认的沉淀代码 (可安全删除)

#### 1. 重复的日志系统
- `simple_logger.h/cpp` - 被 `src/core/utils/logger.h/cpp` 替代
- 功能对比：新版本支持分类、文件输出、性能统计

#### 2. 重复的JSON处理
- `json_parser.h/cpp` - 被 `src/core/utils/json_helper.h` 替代  
- 功能对比：新版本支持链式构建、路径访问、验证

#### 3. 重复的网络客户端
- `network_client.h/cpp` - 被新的网络架构替代
- 功能对比：新架构支持多引擎、负载均衡、性能监控

#### 4. 重复的构建配置
- `CMakeLists_new.txt` - 未使用的构建配置
- 状态：可直接删除

#### 5. 过时的文档文件
- `CLEANUP_SUMMARY.md` - 过时的清理总结
- `CODE_FORMATTING_GUIDE.md` - 过时的格式指南
- `MODULAR_ARCHITECTURE.md` - 过时的架构文档
- `MODULAR_BUILD_CONFIG.md` - 过时的构建文档
- `compile_fix_final.md` - 过时的编译修复文档

#### 6. 可能重复的业务逻辑
- `business_logic.h/cpp` - 需要检查是否被新架构替代
- `service_container.h/cpp` - 需要检查是否被新架构替代
- `request_builder.h/cpp` - 需要检查是否被新架构替代
- `response_processor.h/cpp` - 需要检查是否被新架构替代

### ⚠️ 需要谨慎处理的代码

#### 1. 核心业务代码 (保留)
- `mainwindow.h/cpp/ui` - 主界面，正在使用
- `orderapi.h/cpp` - 核心订单API，正在使用
- `ultrafasttls.h/cpp` - 核心网络引擎，正在使用
- `filterworker.h/cpp` - 过滤器，可能正在使用

#### 2. 工具类 (需要检查)
- `utils.h` - 工具函数，需要检查是否被新工具类替代
- `api_constants.h` - API常量，可能正在使用
- `functional_utils.h` - 函数式工具，需要检查使用情况

#### 3. 服务类 (需要检查)
- `authentication_service.h/cpp` - 认证服务，可能正在使用
- `encryption_service.h/cpp` - 加密服务，可能正在使用
- `error_handler.h/cpp` - 错误处理，可能正在使用

## 🚀 清理执行计划

### 第一阶段：安全删除明确的沉淀代码
1. 删除重复的构建配置
2. 删除过时的文档文件
3. 删除明确被替代的代码文件

### 第二阶段：检查可疑的重复代码
1. 分析业务逻辑模块的使用情况
2. 检查工具类的依赖关系
3. 确认服务类的替代情况

### 第三阶段：更新构建配置
1. 从CMakeLists.txt中移除已删除文件的引用
2. 验证编译通过
3. 测试功能完整性

## 📝 清理检查清单

- [ ] 备份当前代码
- [ ] 删除明确的沉淀代码
- [ ] 更新构建配置
- [ ] 编译测试
- [ ] 功能测试
- [ ] 性能对比测试

## 🎯 预期收益

### 代码质量提升
- 减少代码重复
- 降低维护成本
- 提高代码可读性

### 构建优化
- 减少编译时间
- 减小可执行文件大小
- 简化依赖关系

### 开发效率
- 减少混淆和错误
- 统一代码风格
- 明确架构边界
