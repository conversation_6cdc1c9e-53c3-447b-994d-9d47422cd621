#ifndef FUNCTIONAL_UTILS_H
#define FUNCTIONAL_UTILS_H

#include <QString>
#include <QList>
#include <functional>
#include <algorithm>
#include <numeric>
#include "response_processor.h"

/**
 * @brief 函数式编程工具 - 提供高阶函数和函数组合工具
 * 
 * 函数化设计：每个函数都是纯函数，无副作用，可组合
 */
namespace FunctionalUtils {

    // ==================== 高阶函数 ====================
    
    /**
     * @brief 映射函数 - 将函数应用到列表的每个元素
     */
    template<typename T, typename R>
    QList<R> map(const QList<T>& list, std::function<R(const T&)> func)
    {
        QList<R> result;
        result.reserve(list.size());
        std::transform(list.begin(), list.end(), std::back_inserter(result), func);
        return result;
    }
    
    /**
     * @brief 过滤函数 - 根据谓词过滤列表元素
     */
    template<typename T>
    QList<T> filter(const QList<T>& list, std::function<bool(const T&)> predicate)
    {
        QList<T> result;
        std::copy_if(list.begin(), list.end(), std::back_inserter(result), predicate);
        return result;
    }
    
    /**
     * @brief 归约函数 - 将列表归约为单个值
     */
    template<typename T, typename R>
    R reduce(const QList<T>& list, R initialValue, std::function<R(const R&, const T&)> func)
    {
        return std::accumulate(list.begin(), list.end(), initialValue, func);
    }
    
    /**
     * @brief 查找函数 - 查找满足条件的第一个元素
     */
    template<typename T>
    T findFirst(const QList<T>& list, std::function<bool(const T&)> predicate, const T& defaultValue = T{})
    {
        auto it = std::find_if(list.begin(), list.end(), predicate);
        return it != list.end() ? *it : defaultValue;
    }
    
    /**
     * @brief 分组函数 - 根据键函数对列表元素分组
     */
    template<typename T, typename K>
    QHash<K, QList<T>> groupBy(const QList<T>& list, std::function<K(const T&)> keyFunc)
    {
        QHash<K, QList<T>> groups;
        for (const T& item : list) {
            K key = keyFunc(item);
            groups[key].append(item);
        }
        return groups;
    }
    
    /**
     * @brief 排序函数 - 根据比较函数排序
     */
    template<typename T>
    QList<T> sortBy(const QList<T>& list, std::function<bool(const T&, const T&)> comparator)
    {
        QList<T> result = list;
        std::sort(result.begin(), result.end(), comparator);
        return result;
    }
    
    /**
     * @brief 去重函数 - 移除重复元素
     */
    template<typename T>
    QList<T> unique(const QList<T>& list, std::function<bool(const T&, const T&)> equalFunc = nullptr)
    {
        QList<T> result;
        for (const T& item : list) {
            bool exists = false;
            for (const T& existing : result) {
                if (equalFunc ? equalFunc(item, existing) : item == existing) {
                    exists = true;
                    break;
                }
            }
            if (!exists) {
                result.append(item);
            }
        }
        return result;
    }
    
    /**
     * @brief 分区函数 - 根据谓词将列表分为两部分
     */
    template<typename T>
    QPair<QList<T>, QList<T>> partition(const QList<T>& list, std::function<bool(const T&)> predicate)
    {
        QList<T> trueList, falseList;
        for (const T& item : list) {
            if (predicate(item)) {
                trueList.append(item);
            } else {
                falseList.append(item);
            }
        }
        return qMakePair(trueList, falseList);
    }

    // ==================== 函数组合工具 ====================
    
    /**
     * @brief 函数组合 - 将两个函数组合成一个
     */
    template<typename A, typename B, typename C>
    std::function<C(const A&)> compose(std::function<C(const B&)> f, std::function<B(const A&)> g)
    {
        return [f, g](const A& x) { return f(g(x)); };
    }
    
    /**
     * @brief 管道操作符 - 链式调用函数
     */
    template<typename T>
    class Pipeline
    {
    public:
        explicit Pipeline(const T& value) : m_value(value) {}
        
        template<typename R>
        Pipeline<R> then(std::function<R(const T&)> func) const
        {
            return Pipeline<R>(func(m_value));
        }
        
        T value() const { return m_value; }
        
    private:
        T m_value;
    };
    
    /**
     * @brief 创建管道
     */
    template<typename T>
    Pipeline<T> pipe(const T& value)
    {
        return Pipeline<T>(value);
    }

    // ==================== 订单处理专用函数 ====================
    
    /**
     * @brief 订单过滤器工厂
     */
    class OrderFilters
    {
    public:
        // 价格过滤器
        static std::function<bool(const ResponseProcessor::OrderInfo&)> priceRange(double minPrice, double maxPrice)
        {
            return [minPrice, maxPrice](const ResponseProcessor::OrderInfo& order) {
                return order.price >= minPrice && order.price <= maxPrice;
            };
        }
        
        // 游戏ID过滤器
        static std::function<bool(const ResponseProcessor::OrderInfo&)> gameId(const QString& gameId)
        {
            return [gameId](const ResponseProcessor::OrderInfo& order) {
                return order.gameId == gameId;
            };
        }
        
        // 公开订单过滤器
        static std::function<bool(const ResponseProcessor::OrderInfo&)> publicOnly()
        {
            return [](const ResponseProcessor::OrderInfo& order) {
                return order.isPublic;
            };
        }
        
        // 标题关键词过滤器
        static std::function<bool(const ResponseProcessor::OrderInfo&)> titleContains(const QString& keyword)
        {
            return [keyword](const ResponseProcessor::OrderInfo& order) {
                return order.title.contains(keyword, Qt::CaseInsensitive);
            };
        }
        
        // 组合过滤器
        static std::function<bool(const ResponseProcessor::OrderInfo&)> combine(
            const QList<std::function<bool(const ResponseProcessor::OrderInfo&)>>& filters)
        {
            return [filters](const ResponseProcessor::OrderInfo& order) {
                return std::all_of(filters.begin(), filters.end(), 
                                 [&order](const auto& filter) { return filter(order); });
            };
        }
    };
    
    /**
     * @brief 订单排序器工厂
     */
    class OrderSorters
    {
    public:
        // 按价格排序
        static std::function<bool(const ResponseProcessor::OrderInfo&, const ResponseProcessor::OrderInfo&)> byPrice(bool ascending = true)
        {
            return [ascending](const ResponseProcessor::OrderInfo& a, const ResponseProcessor::OrderInfo& b) {
                return ascending ? a.price < b.price : a.price > b.price;
            };
        }
        
        // 按创建时间排序
        static std::function<bool(const ResponseProcessor::OrderInfo&, const ResponseProcessor::OrderInfo&)> byCreateTime(bool ascending = true)
        {
            return [ascending](const ResponseProcessor::OrderInfo& a, const ResponseProcessor::OrderInfo& b) {
                return ascending ? a.createTime < b.createTime : a.createTime > b.createTime;
            };
        }
        
        // 按标题排序
        static std::function<bool(const ResponseProcessor::OrderInfo&, const ResponseProcessor::OrderInfo&)> byTitle(bool ascending = true)
        {
            return [ascending](const ResponseProcessor::OrderInfo& a, const ResponseProcessor::OrderInfo& b) {
                return ascending ? a.title < b.title : a.title > b.title;
            };
        }
    };
    
    /**
     * @brief 订单统计函数
     */
    class OrderStats
    {
    public:
        // 计算总价值
        static double totalValue(const QList<ResponseProcessor::OrderInfo>& orders)
        {
            return reduce<ResponseProcessor::OrderInfo, double>(orders, 0.0, 
                [](double sum, const ResponseProcessor::OrderInfo& order) {
                    return sum + order.price;
                });
        }
        
        // 计算平均价格
        static double averagePrice(const QList<ResponseProcessor::OrderInfo>& orders)
        {
            if (orders.isEmpty()) return 0.0;
            return totalValue(orders) / orders.size();
        }
        
        // 按游戏分组统计
        static QHash<QString, int> countByGame(const QList<ResponseProcessor::OrderInfo>& orders)
        {
            auto groups = groupBy<ResponseProcessor::OrderInfo, QString>(orders, 
                [](const ResponseProcessor::OrderInfo& order) { return order.gameId; });
            
            QHash<QString, int> counts;
            for (auto it = groups.constBegin(); it != groups.constEnd(); ++it) {
                counts[it.key()] = it.value().size();
            }
            return counts;
        }
        
        // 价格分布统计
        static QHash<QString, int> priceDistribution(const QList<ResponseProcessor::OrderInfo>& orders)
        {
            return groupBy<ResponseProcessor::OrderInfo, QString>(orders, 
                [](const ResponseProcessor::OrderInfo& order) {
                    if (order.price < 10) return QString("0-10");
                    else if (order.price < 50) return QString("10-50");
                    else if (order.price < 100) return QString("50-100");
                    else if (order.price < 500) return QString("100-500");
                    else return QString("500+");
                }).size();
        }
    };

    // ==================== 便利函数 ====================
    
    /**
     * @brief 创建链式操作
     */
    template<typename T>
    QList<T> chain(const QList<T>& list)
    {
        return list;
    }
    
    /**
     * @brief 条件执行
     */
    template<typename T>
    T when(bool condition, std::function<T()> trueFunc, std::function<T()> falseFunc)
    {
        return condition ? trueFunc() : falseFunc();
    }
    
    /**
     * @brief 安全执行（异常处理）
     */
    template<typename T>
    T safeExecute(std::function<T()> func, const T& defaultValue = T{})
    {
        try {
            return func();
        } catch (...) {
            return defaultValue;
        }
    }
}

#endif // FUNCTIONAL_UTILS_H
