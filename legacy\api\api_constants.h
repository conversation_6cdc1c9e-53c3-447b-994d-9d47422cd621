#ifndef API_CONSTANTS_H
#define API_CONSTANTS_H

#include <QString>

/**
 * @brief API相关常量定义
 * 
 * 集中管理所有的API常量，避免魔法字符串和数字
 */
namespace ApiConstants {
    
    // 服务器配置
    const QString BASE_URL = "https://server.dailiantong.com.cn/API/AppService.ashx";
    const QString SERVER_HOST = "server.dailiantong.com.cn";
    const int SERVER_PORT = 443;
    
    // API动作
    namespace Actions {
        const QString USER_TIP_FOR_CHANGE_PASS = "UserTipForChangePass";
        const QString LOGIN = "Login";
        const QString GO_HOME = "GoHome";
        const QString USER_INFO_LIST = "UserInfoList";
        const QString LEVEL_ORDER_LIST = "LevelOrderList";
        const QString NEW_LEVEL_ORDER_ACCEPT = "NewLevelOrderAccept";
        const QString PRE_CHECK = "PreCheck";
        const QString ADD_BLACK = "AddBlack";
        const QString REMOVE_MEMBER = "RemoveMember";
    }
    
    // 网络配置
    namespace Network {
        const int DEFAULT_TIMEOUT_MS = 30000;          // 30秒
        const int CONNECT_TIMEOUT_MS = 10000;          // 10秒连接超时
        const int MAX_RETRY_COUNT = 2;                 // 最大重试次数
        const int RETRY_DELAY_MS = 1000;               // 重试延迟
        const int MAX_RESPONSE_SIZE = 10 * 1024 * 1024; // 10MB最大响应
    }
    
    // 加密配置
    namespace Encryption {
        const QString DEFAULT_ENCODING = "UTF-8";
        const QString HASH_ALGORITHM = "MD5";
        const int PASSWORD_SALT_LENGTH = 16;
    }
    
    // 游戏ID
    namespace GameIds {
        const QString NARUTO = "110";  // 火影忍者
        // 可以添加更多游戏ID
    }
    
    // 错误码
    namespace ErrorCodes {
        const int SUCCESS = 1;
        const int NETWORK_ERROR = -1;
        const int TIMEOUT_ERROR = -2;
        const int PARSE_ERROR = -3;
        const int AUTH_ERROR = -4;
        const int INVALID_PARAMS = -5;
    }
    
    // 响应消息
    namespace Messages {
        const QString LOGIN_SUCCESS = "登录成功";
        const QString NETWORK_ERROR = "网络连接失败";
        const QString TIMEOUT_ERROR = "请求超时";
        const QString PARSE_ERROR = "数据解析失败";
        const QString AUTH_ERROR = "认证失败";
    }
    
    // 用户代理字符串
    namespace UserAgents {
        const QString QUARK_BROWSER = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Quark/4.0";
        const QString CHROME = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
        const QString FIREFOX = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0";
    }
    
    // 请求头
    namespace Headers {
        const QString CONTENT_TYPE = "Content-Type";
        const QString USER_AGENT = "User-Agent";
        const QString ACCEPT = "Accept";
        const QString ACCEPT_LANGUAGE = "Accept-Language";
        const QString ACCEPT_ENCODING = "Accept-Encoding";
        const QString CONNECTION = "Connection";
        const QString CACHE_CONTROL = "Cache-Control";
        
        // 默认值
        const QString DEFAULT_CONTENT_TYPE = "application/x-www-form-urlencoded; charset=UTF-8";
        const QString DEFAULT_ACCEPT = "application/json, text/plain, */*";
        const QString DEFAULT_ACCEPT_LANGUAGE = "zh-CN,zh;q=0.9,en;q=0.8";
        const QString DEFAULT_ACCEPT_ENCODING = "gzip, deflate, br";
        const QString DEFAULT_CONNECTION = "keep-alive";
        const QString DEFAULT_CACHE_CONTROL = "no-cache";
    }
    
    // 文件路径
    namespace Paths {
        const QString LOG_DIR = "logs";
        const QString CONFIG_DIR = "config";
        const QString TEMP_DIR = "temp";
        const QString DEFAULT_LOG_FILE = "api.log";
    }
    
    // 调试配置
    namespace Debug {
        const bool ENABLE_REQUEST_LOGGING = true;
        const bool ENABLE_RESPONSE_LOGGING = true;
        const bool ENABLE_PERFORMANCE_LOGGING = true;
        const int MAX_LOG_MESSAGE_LENGTH = 1000;  // 日志消息最大长度
    }
}

/**
 * @brief 构建完整的API URL
 */
inline QString buildApiUrl(const QString& action) {
    return QString("%1?Action=%2").arg(ApiConstants::BASE_URL, action);
}

/**
 * @brief 构建带参数的API URL
 */
inline QString buildApiUrl(const QString& action, const QHash<QString, QString>& params) {
    QString url = buildApiUrl(action);
    
    if (!params.isEmpty()) {
        QStringList paramList;
        for (auto it = params.constBegin(); it != params.constEnd(); ++it) {
            paramList.append(QString("%1=%2").arg(it.key(), it.value()));
        }
        url += "&" + paramList.join("&");
    }
    
    return url;
}

#endif // API_CONSTANTS_H
