#include "orderapi.h"
#include "../utils/simple_logger.h"
#include "api_constants.h"
// 新架构集成
#include <app_config.h>
#include <logger.h>
// 暂时注释掉新架构的头文件，避免编译错误
// #include "networkengine.h"
// #include "ultrafasttlsengine.h"
// #include "curlengine.h"
#include "../network/ultrafasttls.h"
#include "../network/ultrafasttls_debug_monitor.h"
#include <QNetworkRequest>
#include <QNetworkProxy>
#include <QJsonParseError>
#include <QDebug>
#include <QDateTime>
#include <QThread>
#include <QCryptographicHash>
#include <QUrl>
#include <QStringConverter>
#include <QNetworkAccessManager>
#include <chrono>
#include <QMutex>
#include <QMutexLocker>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <functional>
#include <QTimer>
#include <QSslError>
#include <QJsonArray>
#include <QtConcurrent>
#include <QProcess>
#include <QStandardPaths>
#include <QFileInfo>

// 使用系统zlib头文件
#include <zlib.h>

// 全局直连最近一次网络活动时间（ms）
qint64 OrderAPI::s_lastDirectActivity = 0;

QHash<QString, QNetworkAccessManager*> OrderAPI::s_managerPool; // 定义静态成员

QNetworkAccessManager* OrderAPI::managerForProxy(const QString &host, int port, const QString &type,
                                                 const QString &user, const QString &pass)
{
    // 将在计算 key 之后打印 debug 信息（使用 qDebug，static 函数里不能 emit）
    QString key;
    if (host.isEmpty() || port == 0) {
        key = "direct";
    } else {
        if (!user.isEmpty()) {
            key = QString("%1://%2@%3:%4").arg(type.toLower(), user, host).arg(port);
        } else {
            key = QString("%1://%2:%3").arg(type.toLower(), host).arg(port);
        }
    }
    if (!s_managerPool.contains(key)) {
        auto *mgr = new QNetworkAccessManager;
        if (key != "direct") {
            QNetworkProxy proxy;
            proxy.setType(type.toLower() == "socks5" ? QNetworkProxy::Socks5Proxy : QNetworkProxy::HttpProxy);
            proxy.setHostName(host);
            proxy.setPort(port);
            if (!user.isEmpty() && !pass.isEmpty()) {
                proxy.setUser(user);
                proxy.setPassword(pass);
            }
            mgr->setProxy(proxy);
        }
        s_managerPool.insert(key, mgr);
    }
    return s_managerPool.value(key);
}

OrderAPI::OrderAPI(QObject *parent)
    : QObject(parent)
    , m_keepAliveTimer(nullptr)
    , m_acceptPreheater(nullptr)
    , m_networkManager(nullptr)
    , m_acceptManager(nullptr)
    , m_currentReply(nullptr)
    , m_currentToken("")
    , m_currentUserId("")
    , m_pendingPassword("")
    , m_pendingLoginId("")
    , m_encryptedPayPass("")
    , m_pendingProxyPort(0)
    , m_curlPath("")
{
    // 新架构集成：使用统一配置和日志
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "OrderAPI 初始化开始");
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("网络超时配置: %1ms").arg(NETWORK_CONFIG.timeout));
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("用户代理: %1").arg(NETWORK_CONFIG.userAgent));

    // 自动查找curl路径
    findCurlPath();

    // 临时禁用UltraFastTLS，强制使用稳定的curl
    // NOTE: UltraFastTLS已通过新架构的NetworkManager集成，此处保留curl作为后备方案
    m_networkEngine = NetworkEngine::CURL;
    emit debugLog("[代码整理] 临时禁用UltraFastTLS，使用稳定的curl引擎");

    // 初始化主账号UltraFastTLS（安全模式）
    emit debugLog("[OrderAPI] 🔍 开始创建主账号UltraFastTLS对象...");
    try {
        m_ultraFastTLS = new UltraFastTLS(this);
        emit debugLog("[OrderAPI] ✅ 主账号UltraFastTLS对象创建成功");
        m_ultraFastTLS->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);

        // 启用安静模式，减少日志噪音（类似curl的行为）
        m_ultraFastTLS->setQuietMode(true);

        // 初始化子账号独立UltraFastTLS实例
        emit debugLog("[OrderAPI] 🔍 开始创建子账号独立UltraFastTLS对象...");
        m_subAccountUltraFastTLS = new UltraFastTLS(this);
        emit debugLog("[OrderAPI] ✅ 子账号独立UltraFastTLS对象创建成功");
        m_subAccountUltraFastTLS->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::QUARK_BROWSER);
        m_subAccountUltraFastTLS->setQuietMode(true);

        // 暂时禁用调试监控器，避免卡死
        // m_debugMonitor = new UltraFastTLSDebugMonitor(this);
        // m_ultraFastTLS->setDebugMonitor(m_debugMonitor);

        emit debugLog("⚡ [系统初始化] 优化后的UltraFastTLS引擎已准备就绪，性能优异且稳定");

        connect(m_ultraFastTLS, &UltraFastTLS::debugLog, this, [this](const QString& msg) {
            emit debugLog(msg);
        });

        // 尝试初始化UltraFastTLS
        emit debugLog("[OrderAPI] 🔍 准备调用UltraFastTLS->initialize()...");
        if (m_ultraFastTLS->initialize()) {
            // UltraFastTLS 初始化成功，使用优化模式
            emit debugLog("[OrderAPI] ✅ UltraFastTLS初始化成功，使用优化模式");
            m_networkEngine = NetworkEngine::ULTRA_FAST_TLS;
            emit debugLog("[OrderAPI] 🔄 网络引擎已设置为ULTRA_FAST_TLS");
        } else {
            // UltraFastTLS 初始化失败，使用 curl 作为备用
            emit debugLog("[OrderAPI] ❌ UltraFastTLS初始化失败，回退到curl");
            m_networkEngine = NetworkEngine::CURL;
            emit debugLog("[OrderAPI] 🔄 网络引擎已设置为CURL");
        }
    } catch (const std::exception& e) {
        emit debugLog(QString("[UltraFastTLS] 初始化异常: %1，回退到curl").arg(e.what()));
        m_ultraFastTLS = nullptr;
        m_debugMonitor = nullptr;
        m_networkEngine = NetworkEngine::CURL;
    } catch (...) {
        emit debugLog("[UltraFastTLS] 初始化未知异常，回退到curl");
        m_ultraFastTLS = nullptr;
        m_debugMonitor = nullptr;
        m_networkEngine = NetworkEngine::CURL;
    }



    // 默认直连的 QNAM (长连接)
    m_networkManager = managerForProxy("", 0, "http");

    // 每个实例都需要能收到 finished 信号，使用唯一连接标志防止重复
    connect(m_networkManager, &QNetworkAccessManager::finished, this, &OrderAPI::onNetworkReplyFinished, Qt::UniqueConnection);

    // ------------- 预热：HEAD (一次即可) -------------
    static bool s_preheatedDirect = false;
    if (!s_preheatedDirect) {
        QNetworkRequest warm(QUrl("https://server.dailiantong.com.cn/"));
        warm.setAttribute(QNetworkRequest::Http2AllowedAttribute, true); // 允许 H2
        m_networkManager->head(warm);
        // 预热仅执行一次
        s_preheatedDirect = true;
        markActivity();
    }

    // -------- Keep-Alive --------
    m_keepAliveTimer = new QTimer(this);
    m_keepAliveTimer->setInterval(25000); // 25 s
    connect(m_keepAliveTimer, &QTimer::timeout, this, &OrderAPI::sendKeepAlive);
    m_keepAliveTimer->start();
    
    // -------- 创建抢单专用网络管理器 --------
    m_acceptManager = new QNetworkAccessManager(this);
    
    // -------- 抢单通道预热定时器 --------
    m_acceptPreheater = new QTimer(this);
    m_acceptPreheater->setInterval(25000); // 25秒
    connect(m_acceptPreheater, &QTimer::timeout, this, &OrderAPI::preheatAcceptChannel);
    m_acceptPreheater->start();
    
    // 立即预热一次
    preheatAcceptChannel();
    
    // 连接直接抢单信号到处理槽（使用直接连接，在同一线程处理）
    connect(this, &OrderAPI::takeOrder, this, &OrderAPI::processAcceptOrder, Qt::DirectConnection);
    emit debugLog("[初始化] 已连接快速路径抢单信号，使用Qt::DirectConnection确保在同一线程处理");

    // 默认每次拉取20条订单，可通过 setPageSize 调整
    m_pageSize = 20;
}

OrderAPI::~OrderAPI()
{
    if (m_currentReply) {
        m_currentReply->abort();
        m_currentReply->deleteLater();
    }

    // 清理子账号独立UltraFastTLS实例
    if (m_subAccountUltraFastTLS) {
        delete m_subAccountUltraFastTLS;
        m_subAccountUltraFastTLS = nullptr;
    }

    // 清理新的网络引擎架构 - 暂时注释掉
    // cleanupNewNetworkEngines();
}

void OrderAPI::sendKeepAlive()
{
    qint64 now = QDateTime::currentMSecsSinceEpoch();
    if (now - s_lastDirectActivity < 20000) {
        return; // 20s 内已有网络活动，跳过保活
    }
    QNetworkRequest req(QUrl("https://server.dailiantong.com.cn/"));
    req.setAttribute(QNetworkRequest::Http2AllowedAttribute, true);
    m_networkManager->head(req);
    markActivity();    // 更新活动时间
}

void OrderAPI::markActivity()
{
    s_lastDirectActivity = QDateTime::currentMSecsSinceEpoch();
}

void OrderAPI::loginPreCheck(const QString& account, const QString& proxy, std::function<void(const QString&)> callback) {
    emit debugLog("[PATH] loginPreCheck account=" + account);
    // 解析 proxy 字符串，格式形如 "host:port"，为空则不使用代理
    QString proxyHost;
    int proxyPort = 0;
    if (!proxy.isEmpty()) {
        const QStringList parts = proxy.split(":");
        proxyHost = parts.value(0);
        if (parts.size() > 1) proxyPort = parts.value(1).toInt();
    }
    // 构造POST数据，严格保证参数顺序
    QString timeStamp = QString::number(QDateTime::currentSecsSinceEpoch());
    QStringList params;
    params << "LoginID=" + account
           << "UserID=0"
           << "TimeStamp=" + timeStamp
           << "Ver=1.0"
           << "AppVer=5.0.6"
           << "AppOS=WebApp%20IOS"
           << "AppID=webapp";
    QString data = params.join("&");
    QString sign = signstr("UserTipForChangePass", data, "");
    data += "&Sign=" + sign;

    QNetworkRequest request(QUrl("https://server.dailiantong.com.cn/API/AppService.ashx?Action=UserTipForChangePass"));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
    request.setRawHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1");
    request.setRawHeader("Origin", "https://m.dailiantong.com");
    request.setRawHeader("Referer", "https://m.dailiantong.com/");
    request.setRawHeader("Accept-Encoding", "identity");
    request.setAttribute(QNetworkRequest::Http2AllowedAttribute, true);
    // ... 其他头部可按需补充

    // 使用全局 m_networkManager，避免重复握手
    if (!proxyHost.isEmpty() && proxyPort > 0) {
        QNetworkProxy p;
        p.setType(QNetworkProxy::HttpProxy);
        p.setHostName(proxyHost);
        p.setPort(proxyPort);
        m_networkManager->setProxy(p);
    } else {
        m_networkManager->setProxy(QNetworkProxy::NoProxy);
    }

    QNetworkReply* reply = m_networkManager->post(request, data.toUtf8());
    QObject::connect(reply, &QNetworkReply::finished, this, [reply, callback, this]() {
        QByteArray resp = reply->readAll();
        callback(QString::fromUtf8(resp));
        reply->deleteLater();
        // 复位为 NoProxy，防止影响后续请求
        m_networkManager->setProxy(QNetworkProxy::NoProxy);
    });
}

void OrderAPI::login(const QString& account, const QString& password, const QString& proxy, std::function<void(const QString&)> callback) {
    // 子账号登录开始

    emit debugLog("=== [子账号登录] 开始调试 ===");
    emit debugLog(QString("[子账号登录] 账号: %1").arg(account));
    emit debugLog(QString("[子账号登录] 密码长度: %1").arg(password.length()));
    emit debugLog(QString("[子账号登录] 代理: %1").arg(proxy.isEmpty() ? "无" : proxy));
    emit debugLog("🚀 [子账号登录] 现在也使用优化后的UltraFastTLS引擎！");
    emit debugLog(QString("[子账号登录] 当前网络引擎: %1").arg(
        m_networkEngine == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS" : "curl"));
    
    // 保存登录信息，用于快速抢单
    m_pendingPassword = password;
    
    // 先预检验，获取LoginID
    loginPreCheck(account, proxy, [=](const QString& preCheckResult) {
        // 预检验响应处理

        // 解析LoginID
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(preCheckResult.toUtf8(), &parseError);
        QString loginId;

        if (parseError.error != QJsonParseError::NoError) {
            callback("{\"success\":false,\"message\":\"预检验JSON解析失败\"}");
            return;
        }

        if (doc.isObject()) {
            loginId = doc.object().value("LoginID").toString();
        }
        if (loginId.isEmpty()) {
            callback("{\"success\":false,\"message\":\"获取LoginID失败\"}");
            return;
        }
        
        // 保存登录ID，用于快速抢单
        m_pendingLoginId = loginId;
        
        // 提前计算加密支付密码，用于快速抢单
        m_encryptedPayPass = passwordEncrypt(password, loginId);
        
        QString encryptedPassword = encryptPassword(password, loginId);
        QString timeStamp = QString::number(QDateTime::currentSecsSinceEpoch());

        emit debugLog(QString("[子账号登录] LoginID: %1").arg(loginId));
        emit debugLog(QString("[子账号登录] 加密密码长度: %1").arg(encryptedPassword.length()));
        emit debugLog(QString("[子账号登录] 时间戳: %1").arg(timeStamp));

        // 严格保证参数顺序
        QStringList params;
        params << "LoginID=" + loginId
               << "Pass=" + encryptedPassword
               << "OS=WebApp"
               << "verifystr="
               << "HD="
               << "Channels=web"
               << "UserID=0"
               << "TimeStamp=" + timeStamp
               << "Ver=1.0"
               << "AppVer=5.0.6"
               << "AppOS=WebApp%20IOS"
               << "AppID=webapp";
        QString data = params.join("&");
        QString sign = signstr("GoHome", data, "");
        data += "&Sign=" + sign;
        QNetworkRequest request(QUrl("https://server.dailiantong.com.cn/API/AppService.ashx?Action=GoHome"));
        request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
        request.setRawHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1");
        request.setRawHeader("Origin", "https://m.dailiantong.com");
        request.setRawHeader("Referer", "https://m.dailiantong.com/");
        request.setRawHeader("Accept-Encoding", "identity");
        request.setAttribute(QNetworkRequest::Http2AllowedAttribute, true);
        // ... 其他头部可按需补充

        // 复用与预检验相同的代理设置
        QString proxyHost;
        int proxyPort = 0;
        if (!proxy.isEmpty()) {
            const QStringList parts = proxy.split(":");
            proxyHost = parts.value(0);
            if (parts.size() > 1) proxyPort = parts.value(1).toInt();
        }

        if (!proxyHost.isEmpty() && proxyPort > 0) {
            QNetworkProxy p;
            p.setType(QNetworkProxy::HttpProxy);
            p.setHostName(proxyHost);
            p.setPort(proxyPort);
            m_networkManager->setProxy(p);
        } else {
            m_networkManager->setProxy(QNetworkProxy::NoProxy);
        }

        // 请求参数已设置

        emit debugLog(QString("[子账号登录] 请求URL: %1").arg(request.url().toString()));
        emit debugLog(QString("[子账号登录] 请求数据长度: %1").arg(data.length()));
        emit debugLog(QString("[子账号登录] 请求数据(前100字符): %1").arg(data.left(100)));
        emit debugLog(QString("[子账号登录] 代理设置: %1:%2").arg(proxyHost).arg(proxyPort));

        QNetworkReply* reply = m_networkManager->post(request, data.toUtf8());
        // 网络请求已发送
        emit debugLog("[子账号登录] 网络请求已发送，等待响应...");
        // 不将 m_currentReply 指向登录请求，避免与全局 finished 槽重复读取数据
        QObject::connect(reply, &QNetworkReply::finished, this, [reply, callback, this]() {
            // 收到响应，开始处理
            emit debugLog("[子账号登录] 收到响应，开始处理...");

            // 检查网络错误
            if (reply->error() != QNetworkReply::NoError) {
                // 网络错误处理
                emit debugLog(QString("[子账号登录] 网络错误: %1").arg(reply->errorString()));
            }

            QByteArray resp = reply->readAll();
            // 原始响应字节数处理
            emit debugLog(QString("[子账号登录] 原始响应字节数: %1").arg(resp.size()));

            // 显示原始字节的十六进制表示（前50字节）
            QString hexDump = resp.left(50).toHex(' ');
            // 原始字节hex处理
            emit debugLog(QString("[子账号登录] 原始字节(hex): %1").arg(hexDump));

            // 尝试不同的编码方式
            QString responseText = QString::fromUtf8(resp);
            // 检查是否包含乱码字符
            bool hasGarbledChars = responseText.contains("");
            Q_UNUSED(hasGarbledChars); // 暂时未使用，但保留用于调试

            emit debugLog(QString("[子账号登录] UTF-8解码长度: %1").arg(responseText.length()));
            emit debugLog(QString("[子账号登录] UTF-8解码(前100字符): %1").arg(responseText.left(100)));

            // 检查响应是否包含乱码
            if (responseText.contains("") || responseText.isEmpty()) {
                emit debugLog("[子账号登录] UTF-8解码有问题，尝试Local8Bit编码");
                responseText = QString::fromLocal8Bit(resp);
                emit debugLog(QString("[子账号登录] Local8Bit解码长度: %1").arg(responseText.length()));
                emit debugLog(QString("[子账号登录] Local8Bit解码(前100字符): %1").arg(responseText.left(100)));

                if (responseText.contains("")) {
                    emit debugLog("[子账号登录] Local8Bit也有问题，尝试Latin1编码");
                    responseText = QString::fromLatin1(resp);
                    emit debugLog(QString("[子账号登录] Latin1解码(前100字符): %1").arg(responseText.left(100)));
                }
            }

            // 解析登录响应，保存token和userId
            // 开始JSON解析
            emit debugLog("[子账号登录] 开始JSON解析...");

            QJsonParseError parseError;
            QJsonDocument doc = QJsonDocument::fromJson(responseText.toUtf8(), &parseError);

            if (parseError.error != QJsonParseError::NoError) {
                // JSON解析错误处理

                emit debugLog(QString("[子账号登录] JSON解析错误: %1").arg(parseError.errorString()));
                emit debugLog(QString("[子账号登录] 错误位置: %1").arg(parseError.offset));

                // 尝试用原始字节解析
                doc = QJsonDocument::fromJson(resp, &parseError);
                if (parseError.error != QJsonParseError::NoError) {
                    emit debugLog(QString("[子账号登录] 原始字节JSON解析也失败: %1").arg(parseError.errorString()));
                } else {
                    emit debugLog("[子账号登录] 原始字节JSON解析成功");
                }
            } else {
                emit debugLog("[子账号登录] JSON解析成功");
            }
            if (doc.isObject()) {
                QJsonObject obj = doc.object();
                // JSON对象键处理
                emit debugLog(QString("[子账号登录] JSON对象键: %1").arg(obj.keys().join(", ")));

                int result = obj["Result"].toInt();
                // Result字段处理
                emit debugLog(QString("[子账号登录] Result字段: %1").arg(result));

                if (result == 1) {
                    m_currentToken = obj["Token"].toString();
                    m_currentUserId = QString::number(obj["UserID"].toInt());

                    // 登录成功处理

                    emit debugLog(QString("[子账号登录] 登录成功! Token: %1").arg(m_currentToken.left(10) + "..."));
                    emit debugLog(QString("[子账号登录] UserID: %1").arg(m_currentUserId));

                    // 登录成功后，不立即预热，依靠定时器自动预热
                    // preheatAcceptChannel();
                } else {
                    QString errorMsg = obj["Err"].toString();
                    // 登录失败处理
                    emit debugLog(QString("[子账号登录] 登录失败: %1").arg(errorMsg));
                }
            } else {
                // 响应不是有效的JSON对象
                emit debugLog("[子账号登录] 响应不是有效的JSON对象");
            }

            // 重要修复：返回原始字节数据的UTF-8字符串，避免双重编码
            QString finalResponse = QString::fromUtf8(resp);
            // 最终回调响应处理完成

            emit debugLog(QString("[子账号登录] 最终回调响应(前200字符): %1").arg(finalResponse.left(200)));
            emit debugLog("[子账号登录] === 登录处理完成 ===");
            callback(finalResponse);
            reply->deleteLater();
            m_networkManager->setProxy(QNetworkProxy::NoProxy);
        });
    });
}

void OrderAPI::refreshOrders(const QString &gameId, const QString &token, const QString &userId,
                           const QString &proxyHost, int proxyPort,
                           const QString &proxyType,
                           const QString &proxyUser, const QString &proxyPass,
                           const QString &priceStr,
                           int focusedFlag)
{
    // 子账号API refreshOrders 开始执行

    emit debugLog(QString("[PATH] refreshOrders gameId=%1 proxy=%2:%3 user=%4")
                  .arg(gameId, proxyHost).arg(proxyPort).arg(proxyUser));
    // 🚀 子账号现在也使用UltraFastTLS引擎！
    emit debugLog("🚀 [子账号TLS伪装] 使用优化后的UltraFastTLS引擎进行刷新");
    // 注释掉传统的QNetworkAccessManager
    // m_networkManager = managerForProxy(proxyHost, proxyPort, proxyType, proxyUser, proxyPass);
    // connect(m_networkManager, &QNetworkAccessManager::finished, this, &OrderAPI::onNetworkReplyFinished, Qt::UniqueConnection);
    NEW_LOG_DEBUG(NewLogCategory::API, "refreshOrders 开始");
    NEW_LOG_DEBUG(NewLogCategory::API, QString("gameId: %1").arg(gameId));
    NEW_LOG_DEBUG(NewLogCategory::API, QString("token前10位: %1").arg(token.isEmpty() ? "空" : token.left(10) + "..."));
    NEW_LOG_DEBUG(NewLogCategory::API, QString("userId: %1, proxyHost: %2, proxyPort: %3, proxyType: %4")
                  .arg(userId).arg(proxyHost).arg(proxyPort).arg(proxyType));
    
    emit debugLog(QString("刷新订单 - gameId: %1, userId: %2").arg(gameId).arg(userId));
    
    // ===== 直接发送刷新订单请求（取消 UserOnline 预检） =====

        // 1. 严格保证参数顺序，完全对齐Python OrderedDict
        QList<QPair<QString, QString>> params = {
            {"IsPub", "0"},
            {"GameID", gameId},
            {"ZoneID", "0"},
            {"ServerID", "0"},
            {"SearchStr", ""},
            {"STier", ""},
            {"ETier", ""},
            {"Sort_Str", ""},
            {"PageIndex", "1"},
            {"PageSize", QString::number(m_pageSize)},
            {"Price_Str", priceStr},
            {"PubCancel", "0"},
            {"SettleHour", "0"},
            {"FilterType", "0"},
            {"PGType", "0"},
            {"Focused", QString::number(focusedFlag)},
            {"OrderType", "0"},
            {"PubRecommend", "0"},
            {"Score1", "0"},
            {"Score2", "0"},
            {"UserID", userId},
            {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
            {"Ver", "1.0"},
            {"AppVer", "5.0.6"},
            {"AppOS", "WebApp%20IOS"},
            {"AppID", "webapp"}
        };
        
        // 如果有token，添加到参数中
        if (!token.isEmpty()) {
            params.append({"Token", token});
        }
        
        // 2. 计算签名
        QString action = "LevelOrderList";
        QString sign = signstr(action, params, token);
        params.append({"Sign", sign});
        
        // 3. 构建POST数据
        QString postData = buildPostData(params);
        emit debugLog("POST数据: " + postData);
        NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("POST数据: %1").arg(postData));
        
        // 4. 创建请求
        QNetworkRequest request;
        // 使用与登录相同的域名
        request.setUrl(QUrl("https://server.dailiantong.com.cn/API/AppService.ashx?Action=LevelOrderList"));
        emit debugLog("请求URL: " + request.url().toString());
        
        request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
        request.setRawHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1");
        request.setRawHeader("Accept", "application/json, text/javascript, */*; q=0.01");
        request.setRawHeader("X-Requested-With", "XMLHttpRequest");
        request.setRawHeader("Origin", "https://m.dailiantong.com");
        request.setRawHeader("Referer", "https://m.dailiantong.com/");
        request.setAttribute(QNetworkRequest::Http2AllowedAttribute, true);
        // 明确只接受gzip压缩，避免服务器返回Brotli压缩
        request.setRawHeader("Accept-Encoding", "gzip");
        
        // 连接SSL错误信号
        QObject::connect(m_networkManager, &QNetworkAccessManager::sslErrors, 
                         [this](QNetworkReply* reply, const QList<QSslError>& errors) {
            for (const QSslError& error : errors) {
                emit debugLog("SSL错误: " + error.errorString());
            }
            // 在生产环境中不应该忽略SSL错误，但在测试阶段可以这样做
            reply->ignoreSslErrors();
        });
        
        // 5. 代理已由 managerForProxy 统一配置，无需在此重复设置
        
        // 🚨 重要：子账号必须严格遵守代理配置
        QString response;
        if (proxyHost.isEmpty()) {
            // 无代理：使用优化的UltraFastTLS引擎
            emit debugLog("🚀 [子账号TLS伪装] 无代理，使用独立UltraFastTLS引擎");
            emit debugLog("🔒 [子账号隔离] 使用独立连接池，避免与主账号关联");
            QString fullUrl = QString("https://server.dailiantong.com.cn/API/AppService.ashx?Action=LevelOrderList");
            response = m_subAccountUltraFastTLS->executeRequest(fullUrl, postData);
        } else {
            // 🚨 有代理：必须使用传统QNetworkAccessManager确保代理生效
            emit debugLog(QString("🔒 [子账号代理] 检测到代理%1:%2，必须使用传统网络引擎确保代理生效").arg(proxyHost).arg(proxyPort));
            emit debugLog("⚠️ [安全警告] 代理是子账号隔离的关键，不能忽略");

            // 恢复传统代理方式
            m_networkManager = managerForProxy(proxyHost, proxyPort, proxyType, proxyUser, proxyPass);
            connect(m_networkManager, &QNetworkAccessManager::finished, this, &OrderAPI::onNetworkReplyFinished, Qt::UniqueConnection);

            // 使用传统方式发送请求
            QString url = QString("https://server.dailiantong.com.cn/API/AppService.ashx?Action=LevelOrderList");
            QNetworkRequest request(url);
            request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
            request.setRawHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            if (m_currentReply) {
                m_currentReply->abort();
                m_currentReply->deleteLater();
            }
            m_currentReply = m_networkManager->post(request, postData.toUtf8());
            emit debugLog("📡 [子账号代理] 已通过代理发送请求，等待异步响应");
            return; // 异步处理，直接返回
        }

        if (!response.isEmpty()) {
            emit debugLog(QString("[子账号TLS伪装] 响应内容(前200字符): %1").arg(response.left(200)));

            // 直接解析响应
            QByteArray responseData = response.toUtf8();
            parseOrderRefreshResponse(responseData);

            emit debugLog("🚀 [子账号TLS伪装] 刷新订单请求完成");
        } else {
            emit debugLog("❌ [子账号TLS伪装] 刷新订单请求失败");
        }

        NEW_LOG_DEBUG(NewLogCategory::API, "refreshOrders 结束");
    ;
}

// 新增：严格还原Python协议的参数拼接和签名
QString OrderAPI::buildPostData(const QList<QPair<QString, QString>> &params) {
    // 预估参数长度，避免频繁内存分配
    int estimatedSize = 0;
    for (const auto &pair : params) {
        estimatedSize += pair.first.length() + pair.second.length() + 2; // key + value + "=" + "&"
    }
    
    QString result;
    result.reserve(estimatedSize);
    
    bool isFirst = true;
    for (const auto &pair : params) {
        if (!isFirst) {
            result.append('&');
        }
        result.append(pair.first);
        result.append('=');
        result.append(pair.second);
        isFirst = false;
    }
    
    return result;
}

// 新增：静态基于字符串的signstr实现
QString OrderAPI::signstr(const QString &action, const QString &data, const QString &token)
{
    const QString secretKey = "9c7b9399680658d308691f2acad58c0a";
    NEW_LOG_DEBUG(NewLogCategory::API, "字符串签名计算开始");
    NEW_LOG_DEBUG(NewLogCategory::API, QString("action: %1").arg(action));
    NEW_LOG_DEBUG(NewLogCategory::API, QString("data (前100字符): %1").arg(data.left(100)));
    NEW_LOG_DEBUG(NewLogCategory::API, QString("token前10位: %1").arg(token.isEmpty() ? "空" : token.left(10) + "..."));
    
    // 解析data，拼接所有value（需要URL解码）
    QStringList pairs = data.split('&', Qt::SkipEmptyParts);
    QString t;
    for (const QString &pair : pairs) {
        int idx = pair.indexOf('=');
        if (idx >= 0) {
            QString value = pair.mid(idx + 1);
            value = QUrl::fromPercentEncoding(value.toUtf8());
            t += value;
            NEW_LOG_DEBUG(NewLogCategory::API, QString("%1=%2").arg(pair.left(idx)).arg(value));
        }
    }
    QString toSign = secretKey + action + t + token;
    NEW_LOG_DEBUG(NewLogCategory::API, QString("待签名字符串(前100字符): %1...").arg(toSign.left(100)));
    QByteArray hash = QCryptographicHash::hash(toSign.toUtf8(), QCryptographicHash::Md5);
    QString sign = QString(hash.toHex()).toLower();
    NEW_LOG_DEBUG(NewLogCategory::API, QString("签名结果: %1").arg(sign));
    NEW_LOG_DEBUG(NewLogCategory::API, "字符串签名计算结束");
    return sign;
}

// 基于QList<QPair>的signstr实现
QString OrderAPI::signstr(const QString &action, const QList<QPair<QString, QString>> &params, const QString &token)
{
    // 直接复用buildPostData将params转换为字符串，然后调用字符串版本实现
    QString data = buildPostData(params);
    return signstr(action, data, token);
}

void OrderAPI::acceptOrder(const QString &orderId, const QString &stamp, const QString &token, 
                          const QString &userId, const QString &payPassword, const QString &loginId,
                          const QString &proxyHost, int proxyPort,
                          const QString &proxyUser, const QString &proxyPass)
{
    emit debugLog(QString("[PATH] acceptOrder id=%1 proxy=%2:%3 user=%4")
                  .arg(orderId, proxyHost).arg(proxyPort).arg(proxyUser));
    // 使用与订单对应的代理的 QNAM
    m_networkManager = managerForProxy(proxyHost, proxyPort, "http", proxyUser, proxyPass);
    QString encryptedPayPass = passwordEncrypt(payPassword, loginId);
    
    QUrlQuery params;
    params.addQueryItem("ODSerialNo", orderId);
    params.addQueryItem("Stamp", stamp);
    params.addQueryItem("PayPass", encryptedPayPass);
    params.addQueryItem("Insurance", "0");
    params.addQueryItem("MaxClaimAmount", "0");
    params.addQueryItem("VisitType", "0");
    params.addQueryItem("NonceStr", "");
    params.addQueryItem("NoEnsure", "0");
    params.addQueryItem("IsWx", "0");
    params.addQueryItem("SourceType", "0");
    params.addQueryItem("UserID", userId);
    params.addQueryItem("TimeStamp", getCurrentTimestamp());
    params.addQueryItem("Ver", "1.0");
    params.addQueryItem("AppVer", "5.0.6");
    params.addQueryItem("AppOS", "WebApp%20IOS");
    params.addQueryItem("AppID", "webapp");
    
    // 如果有token，添加到参数中
    if (!token.isEmpty()) {
        params.addQueryItem("Token", token);
    }
    
    // 计算签名并补上 Sign 参数（之前调用 sendRequest 时由 sendRequest 负责）
    QString data = params.toString();
    QString sign = signEncrypt("NewLevelOrderAccept", data, token);
    params.addQueryItem("Sign", sign);
    
    QString url = "https://server.dailiantong.com.cn/API/AppService.ashx?Action=NewLevelOrderAccept";

    // 统一使用共享 m_networkManager，避免额外 TCP / 线程开销

    QUrl fullUrl(url);
    QNetworkRequest req;
    req.setUrl(fullUrl);
    req.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
    req.setRawHeader("Host", "server.dailiantong.com.cn");
    req.setRawHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1");
    req.setRawHeader("Accept", "*/*");
    req.setRawHeader("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
    req.setRawHeader("Origin", "https://m.dailiantong.com");
    req.setRawHeader("Connection", "keep-alive");
    req.setRawHeader("Referer", "https://m.dailiantong.com/");
    req.setRawHeader("Sec-Fetch-Dest", "empty");
    req.setRawHeader("Sec-Fetch-Mode", "cors");
    req.setRawHeader("Sec-Fetch-Site", "cross-site");
    req.setRawHeader("Pragma", "no-cache");
    req.setRawHeader("Cache-Control", "no-cache");
    req.setAttribute(QNetworkRequest::Http2AllowedAttribute, true);

    // managerForProxy 已经完成代理配置，无需再切换

    QByteArray postData = params.toString(QUrl::FullyEncoded).toUtf8();
    QNetworkReply *reply = m_networkManager->post(req, postData);

    connect(reply, &QNetworkReply::finished, this, [this, reply]() {
        QByteArray data = reply->readAll();
        parseOrderAcceptResponse(data);
        reply->deleteLater();
    });
}

void OrderAPI::getUserInfo(const QString &token, const QString &userId, 
                    const QString &proxyHost, int proxyPort)
{
    emit debugLog("获取用户信息 - userId: " + userId);

    QList<QPair<QString, QString>> params = {
        {"UserID", userId},
        {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
        {"Ver", "1.0"},
        {"AppVer", "5.0.6"},
        {"AppOS", "WebApp%20IOS"},
        {"AppID", "webapp"}
    };

    if (!token.isEmpty()) {
        params.append({"Token", token});
    }

    QString action = "UserInfoList";
    QString sign = signstr(action, params, token);
    params.append({"Sign", sign});

    QString postData = buildPostData(params);

    QNetworkRequest request;
    request.setUrl(QUrl("https://server.dailiantong.com.cn/API/AppService.ashx?Action=UserInfoList"));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
    request.setRawHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1");
    request.setRawHeader("Accept", "application/json, text/javascript, */*; q=0.01");
    request.setRawHeader("X-Requested-With", "XMLHttpRequest");
    request.setRawHeader("Origin", "https://m.dailiantong.com");
    request.setRawHeader("Referer", "https://m.dailiantong.com/");

    // 使用共享 m_networkManager
    QNetworkProxy oldProxy = m_networkManager->proxy();
    if (!proxyHost.isEmpty() && proxyPort > 0) {
        QNetworkProxy p; p.setType(QNetworkProxy::HttpProxy); p.setHostName(proxyHost); p.setPort(proxyPort);
        m_networkManager->setProxy(p);
    } else {
        m_networkManager->setProxy(QNetworkProxy::NoProxy);
    }

    QNetworkReply *reply = m_networkManager->post(request, postData.toUtf8());
    connect(reply, &QNetworkReply::finished, this, [this, reply, oldProxy]() {
        QByteArray data = reply->readAll();
        parseUserInfoResponse(data);
        reply->deleteLater();
        m_networkManager->setProxy(oldProxy);
    });
}

// --- sendRequest 已废弃，保留空实现片段以防止链接器残留，但不参与编译 ---
#if 0
void OrderAPI::sendRequest(const QString &url, const QUrlQuery &params, const QString &proxyHost, int proxyPort)
{
    Q_UNUSED(url)
    Q_UNUSED(params)
    Q_UNUSED(proxyHost)
    Q_UNUSED(proxyPort)
}
#endif // sendRequest deleted path

void OrderAPI::setProxy(const QString &host, int port)
{
    QNetworkProxy proxy;
    proxy.setType(QNetworkProxy::HttpProxy);
    proxy.setHostName(host);
    proxy.setPort(port);
    m_networkManager->setProxy(proxy);
}

QString OrderAPI::md5Hash(const QString &input)
{
    QByteArray hash = QCryptographicHash::hash(input.toUtf8(), QCryptographicHash::Md5);
    return hash.toHex();
}

QString OrderAPI::passwordEncrypt(const QString &password, const QString &loginId)
{
    // 缓存相同 (password,loginId) 组合的结果，避免重复计算
    static QHash<QString, QString> cache;
    QString key = password + "|" + loginId;
    if (cache.contains(key)) {
        // 缓存命中，不再输出日志
        return cache.value(key);
    }
    QByteArray pwdMd5 = QCryptographicHash::hash(password.toUtf8(), QCryptographicHash::Md5).toHex();
    QByteArray finalMd5 = QCryptographicHash::hash(pwdMd5 + loginId.toUtf8(), QCryptographicHash::Md5).toHex();
    QString encrypted = QString(finalMd5);
    cache.insert(key, encrypted);
    // 缓存计算完成，关闭日志
    return encrypted;
}

QString OrderAPI::signEncrypt(const QString &action, const QString &data, const QString &token)
{
    QStringList pairs = data.split('&');
    QString unescapedData;
    
    for (const QString &pair : pairs) {
        int equalPos = pair.indexOf('=');
        if (equalPos != -1) {
            QString value = pair.mid(equalPos + 1);
            unescapedData += QUrl::fromPercentEncoding(value.toUtf8());
        }
    }
    
    QString signStr = "9c7b9399680658d308691f2acad58c0a" + action + unescapedData + token;
    return md5Hash(signStr);
}

QString OrderAPI::getCurrentTimestamp()
{
    return QString::number(QDateTime::currentSecsSinceEpoch());
}

void OrderAPI::onNetworkReplyFinished()
{

    // 不使用sender()获取reply对象，直接使用m_currentReply
    QNetworkReply *reply = m_currentReply;
    if (!reply) {
        emit debugLog("onNetworkReplyFinished: 无效的reply对象 (m_currentReply为空)");
        return;
    }



    // -------- 打印协议与压缩信息 --------
    // 删除未使用的HTTP/2检测变量
    QByteArray encHdr = reply->rawHeader("Content-Encoding");
    QString encStr = encHdr.isEmpty() ? "identity" : QString::fromUtf8(encHdr);
    
    QString url = reply->url().toString();
    emit debugLog("收到网络响应: " + url);
    
    if (reply->error() != QNetworkReply::NoError) {
        // 主动取消的请求不视为网络错误，直接忽略
        if (reply->error() == QNetworkReply::OperationCanceledError) {
            emit debugLog("请求已取消，忽略 OperationCanceledError");
        } else {
            emit networkError(reply->errorString());
            emit debugLog("网络错误: " + reply->errorString());
        }
        return;
    }
    
    QByteArray data = reply->readAll();
    bool gz = encStr.compare("gzip", Qt::CaseInsensitive) == 0;
    
    // 不在这里进行解压，而是在后台线程中处理
    // 记录压缩状态，传递给后台线程
    bool isCompressed = gz;
    emit debugLog(QString("响应数据长度: %1 字节").arg(data.size()));
    
    if (data.isEmpty()) {
        emit debugLog("警告: 收到空响应");
        return;
    }
    
    if (url.contains("GoHome")) {
        emit debugLog("处理登录响应");
        parseLoginResponse(data);
    } else if (url.contains("LevelOrderList")) {
        emit debugLog("处理刷新订单响应(后台解压和解析)");
        
        // 使用静态线程池，避免频繁创建线程
        static QThreadPool threadPool;
        if (threadPool.maxThreadCount() < 10) {
            threadPool.setMaxThreadCount(10); // 设置最大线程数
        }
        
        threadPool.start([this, data, isCompressed]() {
            // 在后台线程中进行解压处理
            QByteArray processedData = data;
            
            // 如果是压缩数据，先解压
            if (isCompressed && !data.isEmpty()) {
                QElapsedTimer gzTimer;
                gzTimer.start();
                
                // 使用预分配内存的解压方式，提高效率
                QByteArray decompressed;
                if (data.size() < 1000) {
                    // 小数据包预分配4KB
                    decompressed.reserve(4 * 1024);
                } else if (data.size() < 5000) {
                    // 中等数据包预分配20KB
                    decompressed.reserve(20 * 1024);
    } else {
                    // 大数据包预分配100KB
                    decompressed.reserve(100 * 1024);
                }
                
                decompressed = gunzipData(data);
                qint64 gzElapsed = gzTimer.elapsed();
                
                if (decompressed != data) {
                    QString timestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
                    emit debugLog(QString("[GZIP] %1 解压: %2 -> %3 字节，耗时: %4 ms")
                                 .arg(timestamp)
                                 .arg(data.size())
                                 .arg(decompressed.size())
                                 .arg(gzElapsed));
                    processedData = decompressed;
                }
            }
            
            // 使用解压后的数据进行解析
            parseOrderRefreshResponse(processedData);
        });
    } else if (url.contains("NewLevelOrderAccept")) {
        emit debugLog("处理接单响应");
        parseOrderAcceptResponse(data);
    } else if (url.contains("UserInfoList")) {
        emit debugLog("处理用户信息响应");
        parseUserInfoResponse(data);
    } else {
        emit debugLog("未知响应类型: " + url);
    }
    
    // 处理完成后将m_currentReply置为nullptr并删除
    m_currentReply = nullptr;
    reply->deleteLater();
    
    // 更新时间戳，供保活判断
    markActivity();
}

void OrderAPI::parseLoginResponse(const QByteArray &data)
{
    NEW_LOG_DEBUG(NewLogCategory::API, QString("parseLoginResponse data: %1").arg(QString(data)));
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        emit loginResult(false, "JSON解析错误", "", "", "");
        return;
    }
    QJsonObject obj = doc.object();
    QString err = obj["Err"].toString();
    bool success = obj["Result"].toInt() == 1;
    if (success) {
        QString token = obj["Token"].toString();
        QString userId = QString::number(obj["UserID"].toInt());
        QString uid = obj.contains("UID") ? obj.value("UID").toVariant().toString() : QString();
        emit loginResult(true, err, token, userId, uid);
    } else {
        emit loginResult(false, err, "", "", "");
    }
}

void OrderAPI::parseOrderRefreshResponse(const QByteArray &data)
{
    // parseOrderRefreshResponse 开始执行

    // 统计刷新成功率（静态变量跨调用保存）
    static int s_totalRefresh = 0;
    static int s_successRefresh = 0;
    ++s_totalRefresh;

    // 开始JSON解析
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    if (error.error != QJsonParseError::NoError) {
        // JSON解析失败
        emit orderRefreshResult(false, "JSON解析错误: " + error.errorString(), QJsonArray(), 0);
        emit debugLog(QString("刷新统计: %1 成功 / %2 次").arg(s_successRefresh).arg(s_totalRefresh));
        return;
    }

    // JSON解析成功，开始处理对象
    QJsonObject obj = doc.object();
    
    // 检查返回结果是否成功
    int result = -1;
    QString err;
    if (obj.contains("Result")) {
        // 可能是字符串形式，也可能是数字形式
        if (obj["Result"].isString()) {
            result = obj["Result"].toString().toInt();
        } else {
            result = obj["Result"].toInt();
        }
    }
    if (obj.contains("Err")) {
        err = obj["Err"].toString();
    }
    
    // 不再立即返回错误，而是继续尝试解析订单列表；如果后面能解析到列表，则视为成功
    bool forceSuccess = false;
    
    // 处理数据部分
    QJsonObject dataObj;
    QJsonArray orderList;
    int recordCount = 0;
    
    // 支持两种可能的响应格式
    if (obj.contains("Data") && obj["Data"].isObject()) {
        // 找到Data对象
        dataObj = obj["Data"].toObject();

        // server.dailiantong.com返回的格式
        if (dataObj.contains("LevelOrderList") && dataObj["LevelOrderList"].isArray()) {
            // 找到LevelOrderList数组
            orderList = dataObj["LevelOrderList"].toArray();
            recordCount = dataObj["RecordCount"].toInt();

            // ===== 快速通道：直接在解析线程判断并抢单 =====
            for (const QJsonValue &v : orderList) {
                if (v.isObject()) {
                    // 处理订单对象
                    QJsonObject orderObj = v.toObject();

                    // 发出常规信号用于UI显示 - 使用异步避免死锁
                    // 即将异步发出fastOrderFound信号
                    QMetaObject::invokeMethod(this, [this, orderObj]() {
                        emit fastOrderFound(orderObj);
                    }, Qt::QueuedConnection);
                    // fastOrderFound信号异步发出完成
                    
                    // 判断订单是否符合抢单条件（这里需要实现您的判断逻辑）
                    if (isOrderMatchingCriteria(orderObj)) {
                        // 提取必要参数
                        QString serialNo = orderObj["SerialNo"].toString();
                        QString stamp = orderObj["Stamp"].toString();
                        
                        // 记录开始时间（性能监控）
                        QElapsedTimer timer;
                        timer.start();
                        
                        QString timestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
                        emit debugLog(QString("[快速路径] %1 订单 %2 符合条件，直接在解析线程触发抢单")
                                     .arg(timestamp)
                                     .arg(serialNo));
                        
                        // 异步发出抢单信号避免死锁
                        QString proxyHost = m_networkManager->proxy().hostName();
                        int proxyPort = m_networkManager->proxy().port();
                        QString proxyUser = m_networkManager->proxy().user();
                        QString proxyPass = m_networkManager->proxy().password();

                        QMetaObject::invokeMethod(this, [this, serialNo, stamp, proxyHost, proxyPort, proxyUser, proxyPass]() {
                            emit takeOrder(serialNo, stamp, m_currentUserId,
                                         m_pendingPassword, m_pendingLoginId,
                                         proxyHost, proxyPort, proxyUser, proxyPass);
                        }, Qt::QueuedConnection);
                        
                        // 记录本地处理时间
                        qint64 elapsed = timer.elapsed();
                        emit debugLog(QString("[快速路径] %1 本地处理时间: %2 ms")
                                     .arg(timestamp)
                                     .arg(elapsed));
                    }
                }
            }
            // ===== 快速通道结束 =====
        }
        // 尝试其他可能的字段名
        else if (dataObj.contains("List") && dataObj["List"].isArray()) {
            orderList = dataObj["List"].toArray();
            recordCount = dataObj["RecordCount"].toInt();

            // ===== 快速通道：直接在解析线程判断并抢单 =====
            for (const QJsonValue &v : orderList) {
                if (v.isObject()) {
                    QJsonObject orderObj = v.toObject();
                    
                    // 发出常规信号用于UI显示
                    emit fastOrderFound(orderObj);
                    
                    // 判断订单是否符合抢单条件（这里需要实现您的判断逻辑）
                    if (isOrderMatchingCriteria(orderObj)) {
                        // 提取必要参数
                        QString serialNo = orderObj["SerialNo"].toString();
                        QString stamp = orderObj["Stamp"].toString();
                        
                        // 记录开始时间（性能监控）
                        QElapsedTimer timer;
                        timer.start();
                        
                        QString timestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
                        // 删除qDebug输出
                        // qDebug() << "[快速路径]" << timestamp << "订单" << serialNo << "符合条件，直接在解析线程触发抢单";
                        emit debugLog(QString("[快速路径] %1 订单 %2 符合条件，直接在解析线程触发抢单")
                                     .arg(timestamp)
                                     .arg(serialNo));
                        
                        // 直接发出抢单信号（在同一线程处理）
                        emit takeOrder(serialNo, stamp, m_currentUserId, 
                                     m_pendingPassword, m_pendingLoginId,
                                     m_networkManager->proxy().hostName(),
                                     m_networkManager->proxy().port(),
                                     m_networkManager->proxy().user(),
                                     m_networkManager->proxy().password());
                        
                        // 记录本地处理时间
                        qint64 elapsed = timer.elapsed();
                        // 删除qDebug输出
                        // qDebug() << "[快速路径]" << timestamp << "本地处理时间:" << elapsed << "ms";
                        emit debugLog(QString("[快速路径] %1 本地处理时间: %2 ms")
                                     .arg(timestamp)
                                     .arg(elapsed));
                    }
                }
            }
            // ===== 快速通道结束 =====
        }
        else {
            emit orderRefreshResult(false, "响应格式错误：无法找到订单列表", QJsonArray(), 0);
            emit debugLog(QString("刷新统计: %1 成功 / %2 次").arg(s_successRefresh).arg(s_totalRefresh));
            return;
        }
    }
    // m.dailiantong.com可能直接返回LevelOrderList
    else if (obj.contains("LevelOrderList") && obj["LevelOrderList"].isArray()) {
        orderList = obj["LevelOrderList"].toArray();
        recordCount = obj["RecordCount"].toInt();

        // ===== 快速通道：直接在解析线程判断并抢单 =====
        for (const QJsonValue &v : orderList) {
            if (v.isObject()) {
                QJsonObject orderObj = v.toObject();
                
                // 发出常规信号用于UI显示
                emit fastOrderFound(orderObj);
                
                // 判断订单是否符合抢单条件（这里需要实现您的判断逻辑）
                if (isOrderMatchingCriteria(orderObj)) {
                    // 提取必要参数
                    QString serialNo = orderObj["SerialNo"].toString();
                    QString stamp = orderObj["Stamp"].toString();
                    
                    // 记录开始时间（性能监控）
                    QElapsedTimer timer;
                    timer.start();
                    
                    QString timestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
                    // 删除qDebug输出
                    // qDebug() << "[快速路径]" << timestamp << "订单" << serialNo << "符合条件，直接在解析线程触发抢单";
                    emit debugLog(QString("[快速路径] %1 订单 %2 符合条件，直接在解析线程触发抢单")
                                 .arg(timestamp)
                                 .arg(serialNo));
                    
                    // 直接发出抢单信号（在同一线程处理）
                    emit takeOrder(serialNo, stamp, m_currentUserId, 
                                 m_pendingPassword, m_pendingLoginId,
                                 m_networkManager->proxy().hostName(),
                                 m_networkManager->proxy().port(),
                                 m_networkManager->proxy().user(),
                                 m_networkManager->proxy().password());
                    
                    // 记录本地处理时间
                    qint64 elapsed = timer.elapsed();
                    // 删除qDebug输出
                    // qDebug() << "[快速路径]" << timestamp << "本地处理时间:" << elapsed << "ms";
                    emit debugLog(QString("[快速路径] %1 本地处理时间: %2 ms")
                                 .arg(timestamp)
                                 .arg(elapsed));
                }
            }
        }
        // ===== 快速通道结束 =====
    }
    // 尝试其他可能的字段名
    else if (obj.contains("List") && obj["List"].isArray()) {
        orderList = obj["List"].toArray();
        recordCount = obj["RecordCount"].toInt();

        // ===== 快速通道：直接在解析线程判断并抢单 =====
        for (const QJsonValue &v : orderList) {
            if (v.isObject()) {
                QJsonObject orderObj = v.toObject();
                
                // 发出常规信号用于UI显示
                emit fastOrderFound(orderObj);
                
                // 判断订单是否符合抢单条件（这里需要实现您的判断逻辑）
                if (isOrderMatchingCriteria(orderObj)) {
                    // 提取必要参数
                    QString serialNo = orderObj["SerialNo"].toString();
                    QString stamp = orderObj["Stamp"].toString();
                    
                    // 记录开始时间（性能监控）
                    QElapsedTimer timer;
                    timer.start();
                    
                    QString timestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
                    // 删除qDebug输出
                    // qDebug() << "[快速路径]" << timestamp << "订单" << serialNo << "符合条件，直接在解析线程触发抢单";
                    emit debugLog(QString("[快速路径] %1 订单 %2 符合条件，直接在解析线程触发抢单")
                                 .arg(timestamp)
                                 .arg(serialNo));
                    
                    // 直接发出抢单信号（在同一线程处理）
                    emit takeOrder(serialNo, stamp, m_currentUserId, 
                                 m_pendingPassword, m_pendingLoginId,
                                 m_networkManager->proxy().hostName(),
                                 m_networkManager->proxy().port(),
                                 m_networkManager->proxy().user(),
                                 m_networkManager->proxy().password());
                    
                    // 记录本地处理时间
                    qint64 elapsed = timer.elapsed();
                    // 删除qDebug输出
                    // qDebug() << "[快速路径]" << timestamp << "本地处理时间:" << elapsed << "ms";
                    emit debugLog(QString("[快速路径] %1 本地处理时间: %2 ms")
                                 .arg(timestamp)
                                 .arg(elapsed));
                }
            }
        }
        // ===== 快速通道结束 =====
    }
    else {
        emit orderRefreshResult(false, "响应格式错误：无法找到订单列表", QJsonArray(), 0);
        emit debugLog(QString("刷新统计: %1 成功 / %2 次").arg(s_successRefresh).arg(s_totalRefresh));
        return;
    }
    
    if (!orderList.isEmpty()) {
        forceSuccess = true;
    }
    
    bool treatAsSuccess = (err.trimmed().isEmpty() && orderList.isEmpty());

    // 准备发出最终结果信号
    if (result == 1 || forceSuccess || treatAsSuccess) {
        // 返回成功和订单列表（允许空列表）
        ++s_successRefresh;
        // 即将同步发出orderRefreshResult成功信号

        // 如果是0订单，显示原始响应数据用于调试
        if (orderList.isEmpty() && recordCount == 0) {
            emit debugLog(QString("🔍 0订单调试 - 长度:%1, 内容:%2").arg(data.length()).arg(QString::fromUtf8(data)));
        }

        // 恢复同步信号发射（互斥锁已移除，不会死锁）
        emit orderRefreshResult(true, "获取成功", orderList, recordCount);
        emit debugLog(QString("刷新统计: %1 成功 / %2 次").arg(s_successRefresh).arg(s_totalRefresh));

        // orderRefreshResult成功信号同步发出完成
    } else {
        // 即将同步发出orderRefreshResult失败信号

        // 恢复同步信号发射（互斥锁已移除，不会死锁）
        QString errorMsg = err.isEmpty() ? "未知错误" : err;
        emit debugLog(QString("API错误: 结果=%1, 错误=%2").arg(result).arg(err));
        emit orderRefreshResult(false, errorMsg, QJsonArray(), 0);
        emit debugLog(QString("刷新统计: %1 成功 / %2 次").arg(s_successRefresh).arg(s_totalRefresh));

        // orderRefreshResult失败信号同步发出完成
    }
    // parseOrderRefreshResponse 执行完成
}

void OrderAPI::parseOrderAcceptResponse(const QByteArray &data)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        emit orderAcceptResult(false, "JSON解析错误: " + error.errorString());
        return;
    }
    
    QJsonObject obj = doc.object();
    QString msg;
    QString resultStr = obj.value("Result").toVariant().toString();
    QString codeStr   = obj.value("ReturnCode").toVariant().toString();
    bool success = (resultStr == "1") || (codeStr == "1");
    if (obj.contains("Message")) {
        msg = obj.value("Message").toString();
    } else {
        msg = obj.value("Err").toString();
    }
    if (msg.trimmed().isEmpty()) {
        msg = QString::fromUtf8(data);
    }
    emit orderAcceptResult(success, msg);
}

void OrderAPI::parseUserInfoResponse(const QByteArray &data)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        emit userInfoResult(false, "JSON解析错误: " + error.errorString(), QJsonObject());
        return;
    }
    QJsonObject obj = doc.object();

    // 部分接口将有效载荷放在 Data 字段里
    QJsonObject payload;
    if (obj.contains("Data") && obj.value("Data").isObject()) {
        payload = obj.value("Data").toObject();
    } else {
        payload = obj; // 兼容老格式：直接在顶层
    }

    QString msg;
    QString resultStr = obj.value("Result").toVariant().toString();
    QString codeStr   = obj.value("ReturnCode").toVariant().toString();
    bool success = (resultStr == "1") || (codeStr == "1");
    if (obj.contains("Message")) {
        msg = obj.value("Message").toString();
    } else {
        msg = obj.value("Err").toString();
    }
    if (msg.trimmed().isEmpty()) {
        msg = QString::fromUtf8(data);
    }

    // 额外调试输出（可在界面接收 debugLog 信号）
    emit debugLog(QString("UserInfo success=%1, SumBal=%2, FreezeBal=%3")
                  .arg(success)
                  .arg(payload.value("SumBal").toDouble())
                  .arg(payload.value("FreezeBal").toDouble()));

    emit userInfoResult(success, msg, payload);
}

QString OrderAPI::md5Encrypt(const QString& str) {
    QByteArray hash = QCryptographicHash::hash(str.toUtf8(), QCryptographicHash::Md5);
    return QString(hash.toHex());
}

QString OrderAPI::encryptPassword(const QString& password, const QString& loginId) {
    QByteArray pwdMd5 = QCryptographicHash::hash(password.toUtf8(), QCryptographicHash::Md5).toHex();
    QByteArray finalMd5 = QCryptographicHash::hash(pwdMd5 + loginId.toUtf8(), QCryptographicHash::Md5).toHex();
    return QString(finalMd5);
}

void OrderAPI::focusUser(const QString &focusUserId, const QString &serialNo, const QString &userId,
                         const QString &proxyHost, int proxyPort, const QString &proxyType,
                         const QString &proxyUser, const QString &proxyPass)
{
    QString timestamp = QString::number(QDateTime::currentSecsSinceEpoch());
    QString channel = "1";
    const QString secretKey = "9c7b9399680658d308691f2acad58c0a";
    QString signSource = userId + focusUserId + channel + serialNo + timestamp + secretKey;
    QString sign = md5Encrypt(signSource);

    QUrl url("https://quickorder.dailiantong.com.cn/api/revision/FocusNew");
    QUrlQuery q;
    q.addQueryItem("UserID", userId);
    q.addQueryItem("FocusUserID", focusUserId);
    q.addQueryItem("Channel", channel);
    q.addQueryItem("SerialNo", serialNo);
    q.addQueryItem("timeStamp", timestamp);
    q.addQueryItem("Sign", sign);
    url.setQuery(q);

    QNetworkRequest req(url);
    req.setRawHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1");
    req.setRawHeader("Referer", "https://m.dailiantong.com/");
    req.setRawHeader("Origin", "https://m.dailiantong.com");

    // proxy
    if (!proxyHost.isEmpty() && proxyPort>0) {
        QNetworkProxy proxy;
        proxy.setType(proxyType.toLower()=="socks5" ? QNetworkProxy::Socks5Proxy : QNetworkProxy::HttpProxy);
        proxy.setHostName(proxyHost);
        proxy.setPort(proxyPort);
        if (!proxyUser.isEmpty()) proxy.setUser(proxyUser);
        if (!proxyPass.isEmpty()) proxy.setPassword(proxyPass);
        m_networkManager->setProxy(proxy);
    } else {
        m_networkManager->setProxy(QNetworkProxy::NoProxy);
    }

    QNetworkReply *reply = m_networkManager->get(req);
    connect(reply, &QNetworkReply::finished, [this, reply, focusUserId]() {
        QByteArray data = reply->readAll();
        reply->deleteLater();
        QJsonParseError err;
        QJsonDocument doc = QJsonDocument::fromJson(data, &err);
        if (err.error!=QJsonParseError::NoError) {
            emit focusUserResult(false, "JSON解析错误", focusUserId);
            return;
        }
        QJsonObject obj = doc.object();
        QString msg;
        QString resultStr = obj.value("Result").toVariant().toString();
        QString codeStr   = obj.value("ReturnCode").toVariant().toString();
        bool success = (resultStr == "1") || (codeStr == "1");
        if (obj.contains("Message")) {
            msg = obj.value("Message").toString();
        } else {
            msg = obj.value("Err").toString();
        }
        if (msg.trimmed().isEmpty()) {
            msg = QString::fromUtf8(data);
        }
        emit focusUserResult(success, msg, focusUserId);
    });
}

void OrderAPI::cancelFocusUser(const QString &focusUserId, const QString &userId,
                               const QString &proxyHost, int proxyPort, const QString &proxyType,
                               const QString &proxyUser, const QString &proxyPass)
{
    QString timestamp = QString::number(QDateTime::currentSecsSinceEpoch());
    const QString secretKey = "9c7b9399680658d308691f2acad58c0a";
    QString signSource = userId + focusUserId + timestamp + secretKey;
    QString sign = md5Encrypt(signSource);

    QUrl url("https://quickorder.dailiantong.com.cn/api/revision/CancelFocus");
    QUrlQuery q;
    q.addQueryItem("UserID", userId);
    q.addQueryItem("FocusUserID", focusUserId);
    q.addQueryItem("timeStamp", timestamp);
    q.addQueryItem("Sign", sign);
    url.setQuery(q);

    QNetworkRequest req(url);
    req.setRawHeader("User-Agent", NETWORK_CONFIG.userAgent.toUtf8()); // 使用配置系统
    req.setRawHeader("Referer", "https://m.dailiantong.com/");
    req.setRawHeader("Origin", "https://m.dailiantong.com");

    if (!proxyHost.isEmpty() && proxyPort>0) {
        QNetworkProxy proxy;
        proxy.setType(proxyType.toLower()=="socks5" ? QNetworkProxy::Socks5Proxy : QNetworkProxy::HttpProxy);
        proxy.setHostName(proxyHost);
        proxy.setPort(proxyPort);
        if (!proxyUser.isEmpty()) proxy.setUser(proxyUser);
        if (!proxyPass.isEmpty()) proxy.setPassword(proxyPass);
        m_networkManager->setProxy(proxy);
    } else {
        m_networkManager->setProxy(QNetworkProxy::NoProxy);
    }

    QNetworkReply *reply = m_networkManager->get(req);
    connect(reply, &QNetworkReply::finished, [this, reply, focusUserId]() {
        QByteArray data = reply->readAll();
        reply->deleteLater();
        QJsonParseError err;
        QJsonDocument doc = QJsonDocument::fromJson(data, &err);
        if (err.error!=QJsonParseError::NoError) {
            emit cancelFocusUserResult(false, "JSON解析错误", focusUserId);
            return;
        }
        QJsonObject obj = doc.object();
        QString msg;
        QString resultStr = obj.value("Result").toVariant().toString();
        QString codeStr   = obj.value("ReturnCode").toVariant().toString();
        bool success = (resultStr == "1") || (codeStr == "1");
        if (obj.contains("Message")) {
            msg = obj.value("Message").toString();
        } else {
            msg = obj.value("Err").toString();
        }
        if (msg.trimmed().isEmpty()) {
            msg = QString::fromUtf8(data);
        }
        emit cancelFocusUserResult(success, msg, focusUserId);
    });
}

void OrderAPI::addBlackUser(const QString &memberUserId, const QString &userId, const QString &token,
                            const QString &proxyHost, int proxyPort, const QString &proxyType,
                            const QString &proxyUser, const QString &proxyPass)
{
    QList<QPair<QString, QString>> params = {
        {"UserID", userId},
        {"MemberUserID", memberUserId},
        {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
        {"Ver", "1.0"},
        {"AppVer", "5.0.6"},
        {"AppOS", "WebApp%20IOS"},
        {"AppID", "webapp"}
    };
    if (!token.isEmpty()) params.append({"Token", token});
    QString sign = signstr("AddBlack", params, token);
    params.append({"Sign", sign});
    QString postData = buildPostData(params);

    QNetworkRequest req(QUrl("https://server.dailiantong.com.cn/API/AppService.ashx?Action=AddBlack"));
    req.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
    req.setRawHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1");

    if (!proxyHost.isEmpty() && proxyPort>0) {
        QNetworkProxy proxy;
        proxy.setType(proxyType.toLower()=="socks5"?QNetworkProxy::Socks5Proxy:QNetworkProxy::HttpProxy);
        proxy.setHostName(proxyHost);proxy.setPort(proxyPort);
        if(!proxyUser.isEmpty()) proxy.setUser(proxyUser);
        if(!proxyPass.isEmpty()) proxy.setPassword(proxyPass);
        m_networkManager->setProxy(proxy);
    } else {
        m_networkManager->setProxy(QNetworkProxy::NoProxy);
    }

    QNetworkReply* reply = m_networkManager->post(req, postData.toUtf8());
    connect(reply, &QNetworkReply::finished, [this, reply, memberUserId]() {
        QByteArray data = reply->readAll();
        reply->deleteLater();
        QJsonParseError err; QJsonDocument doc=QJsonDocument::fromJson(data,&err);
        bool success=false; QString msg;
        if(err.error==QJsonParseError::NoError && doc.isObject()) {
            QJsonObject o=doc.object();
            QString rs=o.value("Result").toVariant().toString();
            QString rc=o.value("ReturnCode").toVariant().toString();
            success = (rs=="1"||rc=="1");
            msg = o.contains("Message")?o.value("Message").toString():o.value("Err").toString();
        }
        if(msg.trimmed().isEmpty()) msg = QString::fromUtf8(data);
        emit addBlackResult(success,msg,memberUserId);
    });
}

void OrderAPI::removeBlackUser(const QString &memberUserId, const QString &userId, const QString &token,
                               const QString &proxyHost, int proxyPort, const QString &proxyType,
                               const QString &proxyUser, const QString &proxyPass)
{
    QList<QPair<QString, QString>> params = {
        {"MemberUserID", memberUserId},
        {"UserID", userId},
        {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
        {"Ver", "1.0"},
        {"AppVer", "5.0.6"},
        {"AppOS", "WebApp%20IOS"},
        {"AppID", "webapp"}
    };
    if (!token.isEmpty()) params.append({"Token", token});
    QString sign = signstr("RemoveMember", params, token);
    params.append({"Sign", sign});
    QString postData = buildPostData(params);
    QNetworkRequest req(QUrl("https://server.dailiantong.com.cn/API/AppService.ashx?Action=RemoveMember"));
    req.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
    req.setRawHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1");
    if (!proxyHost.isEmpty() && proxyPort>0) {
        QNetworkProxy proxy;proxy.setType(proxyType.toLower()=="socks5"?QNetworkProxy::Socks5Proxy:QNetworkProxy::HttpProxy);
        proxy.setHostName(proxyHost);proxy.setPort(proxyPort);
        if(!proxyUser.isEmpty()) proxy.setUser(proxyUser);
        if(!proxyPass.isEmpty()) proxy.setPassword(proxyPass);
        m_networkManager->setProxy(proxy);
    } else {m_networkManager->setProxy(QNetworkProxy::NoProxy);}    
    QNetworkReply* reply=m_networkManager->post(req,postData.toUtf8());
    connect(reply,&QNetworkReply::finished,[this,reply,memberUserId](){
        QByteArray data=reply->readAll();reply->deleteLater();
        QJsonParseError err;QJsonDocument doc=QJsonDocument::fromJson(data,&err);
        bool success=false;QString msg;
        if(err.error==QJsonParseError::NoError && doc.isObject()) {
            QJsonObject o=doc.object();QString rs=o.value("Result").toVariant().toString();
            QString rc=o.value("ReturnCode").toVariant().toString();
            success=(rs=="1"||rc=="1");
            msg=o.contains("Message")?o.value("Message").toString():o.value("Err").toString();
        }
        if(msg.trimmed().isEmpty()) msg=QString::fromUtf8(data);
        emit removeBlackResult(success,msg,memberUserId);
    });
} 

QByteArray OrderAPI::gunzipData(const QByteArray &compressedData)
{
    // 如果数据为空，直接返回空
    if (compressedData.isEmpty())
        return QByteArray();
    
    // 初始化zlib解压缩流
    z_stream strm;
    memset(&strm, 0, sizeof(strm));
    
    // 设置输入数据
    strm.next_in = (Bytef*)compressedData.constData();
    strm.avail_in = compressedData.size();
    
    // 初始化解压缩
    if (inflateInit2(&strm, 15 + 32) != Z_OK) {  // 15 + 32 表示自动检测gzip格式
        emit debugLog("zlib初始化失败");
        return compressedData;
    }
    
    // 准备输出缓冲区 - 使用更大的缓冲区提高效率
    QByteArray result;
    const int CHUNK = 16384; // 16KB缓冲区，比原来的4KB更大
    char outBuffer[CHUNK];
    int status;
    
    // 解压缩循环
    do {
        strm.next_out = (Bytef*)outBuffer;
        strm.avail_out = CHUNK;
        
        status = inflate(&strm, Z_NO_FLUSH);
        
        if (status == Z_OK || status == Z_STREAM_END) {
            result.append(outBuffer, CHUNK - strm.avail_out);
        } else {
            inflateEnd(&strm);
            return compressedData; // 出错时返回原始数据
        }
        
    } while (status != Z_STREAM_END && strm.avail_out == 0);
    
    // 清理
    inflateEnd(&strm);
    
    if (status != Z_STREAM_END) {
        return compressedData; // 解压未完成时返回原始数据
    }
    
    return result;
} 

void OrderAPI::preheatAcceptChannel()
{
    // 防止重复预热 - 添加静态变量跟踪正在进行的预热
    static bool s_preheatInProgress = false;
    if (s_preheatInProgress) {
        // 已有预热请求在进行中，跳过本次预热
        return;
    }
    s_preheatInProgress = true;
    
    // 创建预热请求
    QNetworkRequest req(QUrl("https://server.dailiantong.com.cn/"));
    req.setAttribute(QNetworkRequest::Http2AllowedAttribute, true);
    
    // 发送轻量级HEAD请求
    QNetworkReply* reply = m_acceptManager->head(req);
    
    // 输出预热开始日志，包含时间戳
    QString timestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
    emit debugLog(QString("[预热] %1 开始预热抢单专用通道").arg(timestamp));
    
    // 处理响应
    connect(reply, &QNetworkReply::finished, [reply, this]() {
        // 输出预热状态
        bool http2Used = reply->attribute(QNetworkRequest::Http2WasUsedAttribute).toBool();
        QString timestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
        emit debugLog(QString("[预热] %1 抢单专用通道预热完成，HTTP/2=%2")
                     .arg(timestamp)
                     .arg(http2Used ? "是" : "否"));
        reply->deleteLater();
        
        // 重置预热状态标志
        s_preheatInProgress = false;
    });
} 

// 判断订单是否符合抢单条件
bool OrderAPI::isOrderMatchingCriteria(const QJsonObject &orderObj)
{
    // NOTE: 订单筛选逻辑已通过FilterWorker和UI层的高级筛选实现
    // 此处预留接口供未来扩展服务端筛选功能
    // 这里只是一个示例，您需要根据实际需求修改
    
    // 示例：检查订单价格是否在可接受范围内
    double price = orderObj["Price"].toString().toDouble();
    if (price <= 0) return false;
    
    // 示例：检查订单状态
    int status = orderObj["Status"].toInt();
    if (status != 0) return false; // 假设0表示可接单状态
    
    // 如果通过所有检查，返回true
    return true;
}

// 直接处理抢单请求（在解析线程中执行）
void OrderAPI::processAcceptOrder(const QString &serialNo, const QString &stamp,
                                 const QString &userId, const QString &payPass,
                                 const QString &loginId, const QString &proxyHost, 
                                 int proxyPort, const QString &proxyUser, const QString &proxyPass)
{
    // 参数payPass和loginId在使用预计算的加密密码时不再需要，但保留参数以保持接口一致
    Q_UNUSED(payPass);
    Q_UNUSED(loginId);
    
    // 记录开始时间（性能监控）
    QElapsedTimer timer;
    timer.start();
    
    QString startTimestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
    emit debugLog(QString("[抢单] %1 开始处理抢单请求 [%2]").arg(startTimestamp).arg(serialNo));
    
    // 构建超精简请求参数 - 只保留绝对必要参数
    static QString baseParams; // 静态缓存基础参数字符串
    static QString lastToken;
    
    // 只有在token变化时才重新构建基础参数
    if (baseParams.isEmpty() || lastToken != m_currentToken) {
        QUrlQuery baseQuery;
        baseQuery.addQueryItem("PayPass", m_encryptedPayPass);
        baseQuery.addQueryItem("UserID", userId);
        if (!m_currentToken.isEmpty()) {
            baseQuery.addQueryItem("Token", m_currentToken);
        }
        baseParams = baseQuery.toString();
        lastToken = m_currentToken;
    }
    
    // 构建最小化请求 - 只添加变化的参数
    QString timestamp = getCurrentTimestamp();
    QString postData = QString("ODSerialNo=%1&Stamp=%2&TimeStamp=%3&%4")
                      .arg(serialNo, stamp, timestamp, baseParams);
    
    // 计算签名并直接添加
    QString sign = signEncrypt("NewLevelOrderAccept", postData, m_currentToken);
    postData += "&Sign=" + sign;
    
    // 使用静态请求对象 - 避免每次创建新对象
    static QNetworkRequest req;
    static bool reqInitialized = false;
    
    if (!reqInitialized) {
        req = QNetworkRequest(QUrl("https://server.dailiantong.com.cn/API/AppService.ashx?Action=NewLevelOrderAccept"));
        req.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
        req.setRawHeader("Origin", "https://m.dailiantong.com"); // 保留Origin防止CORS问题
        req.setAttribute(QNetworkRequest::Http2AllowedAttribute, true);
        req.setPriority(QNetworkRequest::HighPriority); // 设置高优先级
        reqInitialized = true;
    }
    
    // 使用静态代理对象 - 只在代理配置变化时更新
    static QString lastProxyHost;
    static int lastProxyPort = 0;
    static QString lastProxyUser;
    static QString lastProxyPass;
    
    // 只有在代理配置变化时才更新
    if (proxyHost != lastProxyHost || proxyPort != lastProxyPort || 
        proxyUser != lastProxyUser || proxyPass != lastProxyPass) {
        
        if (!proxyHost.isEmpty() && proxyPort > 0) {
            QNetworkProxy proxy;
            proxy.setType(QNetworkProxy::HttpProxy);
            proxy.setHostName(proxyHost);
            proxy.setPort(proxyPort);
            if (!proxyUser.isEmpty()) proxy.setUser(proxyUser);
            if (!proxyPass.isEmpty()) proxy.setPassword(proxyPass);
            m_acceptManager->setProxy(proxy);
        } else {
            m_acceptManager->setProxy(QNetworkProxy::NoProxy);
        }
        
        // 更新缓存的代理配置
        lastProxyHost = proxyHost;
        lastProxyPort = proxyPort;
        lastProxyUser = proxyUser;
        lastProxyPass = proxyPass;
    }
    
    // 使用专用网络管理器发送请求 - 直接使用预构建的postData
    QNetworkReply* reply = m_acceptManager->post(req, postData.toUtf8());
    
    // 使用静态事件循环和定时器 - 避免每次创建新对象
    static QEventLoop loop;
    static QTimer timeoutTimer;
    static bool timerInitialized = false;
    
    if (!timerInitialized) {
        timeoutTimer.setSingleShot(true);
        connect(&timeoutTimer, &QTimer::timeout, &loop, &QEventLoop::quit);
        timerInitialized = true;
    }
    
    // 连接当前请求的信号
    connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
    
    // 设置超时（减少到1.5秒 - 如果超过这个时间，订单可能已经被抢走）
    timeoutTimer.start(1500);
    
    // 等待响应
    loop.exec();
    
    // 断开连接，避免信号积累
    disconnect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
    
    // 处理响应
    if (reply->isFinished() && !timeoutTimer.isActive()) {
        emit debugLog("抢单请求超时");
        QMetaObject::invokeMethod(this, "reportAcceptResult", 
                                Qt::QueuedConnection,
                                Q_ARG(bool, false),
                                Q_ARG(QString, "请求超时"),
                                Q_ARG(QString, serialNo));
    } else if (reply->error() != QNetworkReply::NoError) {
        emit debugLog(QString("抢单请求错误: %1").arg(reply->errorString()));
        QMetaObject::invokeMethod(this, "reportAcceptResult", 
                                Qt::QueuedConnection,
                                Q_ARG(bool, false),
                                Q_ARG(QString, reply->errorString()),
                                Q_ARG(QString, serialNo));
    } else {
        QByteArray responseData = reply->readAll();
        
        // 解析响应
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(responseData, &error);
        bool success = false;
        QString message = "未知错误";
        
        if (error.error == QJsonParseError::NoError && doc.isObject()) {
            QJsonObject obj = doc.object();
            QString resultStr = obj.value("Result").toVariant().toString();
            QString codeStr = obj.value("ReturnCode").toVariant().toString();
            success = (resultStr == "1") || (codeStr == "1");
            
            if (obj.contains("Message")) {
                message = obj.value("Message").toString();
            } else {
                message = obj.value("Err").toString();
            }
            
            if (message.trimmed().isEmpty()) {
                message = QString::fromUtf8(responseData);
            }
        } else {
            message = "JSON解析错误: " + error.errorString();
        }
        
        // 发送结果信号回UI线程（使用队列连接确保线程安全）
        QMetaObject::invokeMethod(this, "reportAcceptResult", 
                                Qt::QueuedConnection,
                                Q_ARG(bool, success),
                                Q_ARG(QString, message),
                                Q_ARG(QString, serialNo));
    }
    
    // 记录性能指标
    qint64 elapsed = timer.elapsed();
    QString endTimestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
    QMetaObject::invokeMethod(this, "logPerformance",
                            Qt::QueuedConnection,
                            Q_ARG(QString, QString("[抢单] %1 抢单总耗时: %2 ms，订单号: %3")
                                  .arg(endTimestamp)
                                  .arg(elapsed)
                                  .arg(serialNo)));
    
    // 清理资源
    reply->deleteLater();
}

// 处理抢单结果（在UI线程执行）
void OrderAPI::reportAcceptResult(bool success, const QString &message, const QString &serialNo)
{
    // 发出常规接口信号
    emit orderAcceptResult(success, message);
    
    // 记录日志
    QString timestamp = QTime::currentTime().toString("hh:mm:ss.zzz");
    emit debugLog(QString("[抢单结果] %1 %2 - 订单: %3 - %4")
                 .arg(timestamp)
                 .arg(success ? "成功" : "失败")
                 .arg(serialNo)
                 .arg(message));
}

// 记录性能日志
void OrderAPI::logPerformance(const QString &message)
{
    emit debugLog(message);
}

// TLS指纹伪装相关实现
QString OrderAPI::buildTlsFingerprint()
{
    // 简化TLS配置，确保连接成功
    // 先使用最基本的配置，确保能正常连接

    return QString("--tlsv1.2 "                              // 只使用TLS 1.2，更兼容
                  "--compressed ");                           // 启用压缩
}

QString OrderAPI::executeCurlRequest(const QString &url, const QString &postData, const QString &headers)
{
    // executeCurlRequest 开始执行
    LOG_DEBUG(LogCategory::NETWORK, QString("使用curl路径: %1").arg(m_curlPath));

    // 记录开始时间
    auto startTime = std::chrono::high_resolution_clock::now();

    // 检查curl文件是否存在
    QFileInfo curlFile(m_curlPath);
    if (!curlFile.exists()) {
        LOG_ERROR(LogCategory::NETWORK, QString("curl文件不存在: %1").arg(m_curlPath));
        return QString();
    }

    // 首次使用时测试curl版本和基本连接
    static bool curlTested = false;
    if (!curlTested) {
        QProcess testProcess;
        testProcess.start(m_curlPath, QStringList() << "--version");
        if (testProcess.waitForFinished(5000)) {
            QString version = testProcess.readAllStandardOutput();
            LOG_INFO(LogCategory::NETWORK, QString("curl版本: %1").arg(version.split('\n').first()));

            // 测试基本连接
            QProcess connTest;
            QStringList testArgs;
            testArgs << "--silent" << "--head" << "--max-time" << "10" << "--connect-timeout" << "5"
                     << "https://server.dailiantong.com.cn/";
            connTest.start(m_curlPath, testArgs);
            if (connTest.waitForFinished(12000) && connTest.exitCode() == 0) {
                LOG_DEBUG(LogCategory::NETWORK, "服务器连接测试成功");
            } else {
                LOG_WARNING(LogCategory::NETWORK, QString("服务器连接测试失败，退出码: %1").arg(connTest.exitCode()));
            }
        } else {
            LOG_ERROR(LogCategory::NETWORK, "curl版本测试失败");
        }
        curlTested = true;
    }

    QProcess process;
    QStringList arguments;

    // 基本curl参数 - 增加超时时间，添加重试和连接超时
    arguments << "--silent" << "--show-error" << "--location"
              << "--max-time" << "60"           // 总超时60秒
              << "--connect-timeout" << "30"    // 连接超时30秒
              << "--retry" << "2"               // 重试2次
              << "--retry-delay" << "1";        // 重试间隔1秒

    // 暂时移除Cookie支持，避免curl进程阻塞
    // arguments << "--cookie-jar" << "-" << "--cookie" << "-";  // 可能导致阻塞

    // TLS指纹伪装参数
    arguments << buildTlsFingerprint().split(" ", Qt::SkipEmptyParts);

    // 强制使用HTTP/1.1 - 匹配真实夸克浏览器请求
    arguments << "--http1.1";

    // 启用自动解压缩 - 让curl自动处理gzip/deflate
    arguments << "--compressed";

    // 移除-i参数，避免头部和响应体混合导致的解析问题
    // arguments << "-i";

    // 根据指纹选择设置不同的用户代理和头部
    if (m_mainAccountFingerprint == WECHAT_FINGERPRINT) {
        // 微信浏览器指纹 (基于真实抓包数据优化)
        arguments << "-H" << "User-Agent: Mozilla/5.0 (Linux; Android 15; V2307A Build/AP3A.240905.015.A1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.136 Mobile Safari/537.36 XWEB/1340153 MMWEBSDK/******** MMWEBID/7101 MicroMessenger/8.0.61.2880(0x28003D57) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64";
        arguments << "-H" << "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/tpg,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";
        arguments << "-H" << "Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7";
        arguments << "-H" << "Accept-Encoding: gzip, deflate";
        arguments << "-H" << "Origin: https://m.dailiantong.com";
        arguments << "-H" << "Referer: https://m.dailiantong.com/";
        arguments << "-H" << "Connection: keep-alive";
        arguments << "-H" << "Upgrade-Insecure-Requests: 1";  // 微信浏览器特有头部
        arguments << "-H" << "X-Requested-With: com.tencent.mm";  // 微信标识
        arguments << "-H" << "Sec-Fetch-Site: cross-site";
        arguments << "-H" << "Sec-Fetch-Mode: cors";
        arguments << "-H" << "Sec-Fetch-Dest: empty";
        // 微信WebView的Client Hints
        arguments << "-H" << "sec-ch-ua: \"Android WebView\";v=\"134\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"134\"";
        arguments << "-H" << "sec-ch-ua-platform: \"Android\"";
        arguments << "-H" << "sec-ch-ua-mobile: ?1";
    } else {
        // 夸克浏览器指纹 (默认)
        arguments << "-H" << "User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-CN; V2307A Build/AP3A.240905.015.A1) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.80 Quark/7.14.5.880 Mobile Safari/537.36";
        arguments << "-H" << "Accept: */*";
        arguments << "-H" << "Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7";
        arguments << "-H" << "Accept-Encoding: gzip, deflate, br";
        arguments << "-H" << "Origin: https://m.dailiantong.com";
        arguments << "-H" << "Referer: https://m.dailiantong.com/";
        arguments << "-H" << "Connection: keep-alive";
        arguments << "-H" << "X-Requested-With: com.quark.browser";  // 夸克浏览器标识
        arguments << "-H" << "Sec-Fetch-Site: cross-site";
        arguments << "-H" << "Sec-Fetch-Mode: cors";
        arguments << "-H" << "Sec-Fetch-Dest: empty";
        // 夸克浏览器WebView的Client Hints
        arguments << "-H" << "sec-ch-ua: \"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"";
        arguments << "-H" << "sec-ch-ua-platform: \"Android\"";
        arguments << "-H" << "sec-ch-ua-mobile: ?1";
    }

    // 添加自定义头部
    if (!headers.isEmpty()) {
        QStringList headerList = headers.split("\n", Qt::SkipEmptyParts);
        for (const QString &header : headerList) {
            arguments << "-H" << header.trimmed();
        }
    }

    // POST数据
    if (!postData.isEmpty()) {
        arguments << "-X" << "POST";
        arguments << "-H" << "Content-Type: application/x-www-form-urlencoded";
        arguments << "--data-raw" << postData;
    }

    // URL
    arguments << url;

    LOG_DEBUG(LogCategory::NETWORK, QString("执行curl请求: %1").arg(url));
    LOG_DEBUG(LogCategory::NETWORK, QString("curl参数数量: %1").arg(arguments.size()));

    // 调试模式：输出完整的curl命令（仅显示前几个参数避免日志过长）
    QString debugCmd = QString("\"%1\"").arg(m_curlPath);
    for (int i = 0; i < qMin(10, arguments.size()); ++i) {
        debugCmd += " " + arguments[i];
    }
    if (arguments.size() > 10) debugCmd += " ...";
    LOG_DEBUG(LogCategory::NETWORK, QString("curl命令: %1").arg(debugCmd));

    process.start(m_curlPath, arguments);
    if (!process.waitForFinished(70000)) {  // 70秒超时，比curl的60秒稍长
        LOG_ERROR(LogCategory::NETWORK, "curl请求超时");
        process.kill();  // 强制终止进程
        return QString();
    }

    // 记录结束时间
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    if (process.exitCode() != 0) {
        QString error = process.readAllStandardError();
        QString output = process.readAllStandardOutput();
        LOG_ERROR(LogCategory::NETWORK, QString("curl请求失败，退出码: %1，耗时: %2ms").arg(process.exitCode()).arg(duration.count()));
        LOG_ERROR(LogCategory::NETWORK, QString("错误信息: %1").arg(error.isEmpty() ? "无" : error));
        LOG_DEBUG(LogCategory::NETWORK, QString("输出信息: %1").arg(output.isEmpty() ? "无" : output.left(200)));

        // 记录失败统计
        QMutexLocker locker(&m_statsMutex);
        m_performanceStats.curlRequests++;
        m_performanceStats.curlFailures++;
        m_performanceStats.curlTotalTime += duration.count();
        m_performanceStats.curlAverageTime = m_performanceStats.curlTotalTime / m_performanceStats.curlRequests;

        return QString();
    }

    // 读取响应并修复中文乱码问题
    QByteArray responseData = process.readAllStandardOutput();

    // 明确标识使用curl引擎
    emit debugLog("🔧 [CURL引擎] 开始处理响应数据");
    emit debugLog(QString("🔧 [CURL引擎] 原始数据长度: %1 字节").arg(responseData.size()));
    emit debugLog(QString("🔧 [CURL引擎] 原始数据前20字节(hex): %1").arg(responseData.left(20).toHex()));

    // 检查是否包含中文乱码
    QString testResponse = QString::fromUtf8(responseData);
    bool hasGarbledChars = testResponse.contains("鐧") || testResponse.contains("鎴") || testResponse.contains("鏂");
    emit debugLog(QString("🔧 [CURL引擎] 包含乱码字符: %1").arg(hasGarbledChars ? "是" : "否"));

    // 简化处理：curl的--compressed参数应该已经自动处理了gzip解压缩
    QString response = QString::fromUtf8(responseData);

    emit debugLog(QString("[curl数据包分析] 响应处理完成，长度: %1 字符").arg(response.length()));

    // 检查是否仍有编码问题
    if (hasGarbledChars) {
        emit debugLog("[curl数据包分析] ⚠️ 仍然检测到乱码，尝试其他编码方式");

        // 尝试GBK编码（Qt 6方式）
        auto gbkDecoder = QStringDecoder(QStringDecoder::System);
        if (gbkDecoder.isValid()) {
            QString gbkResponse = gbkDecoder(responseData);
            bool gbkHasGarbled = gbkResponse.contains("鐧") || gbkResponse.contains("鎴") || gbkResponse.contains("鏂");
            if (!gbkHasGarbled) {
                emit debugLog("🔧 [CURL引擎] ✅ 系统编码解决了乱码问题");
                response = gbkResponse;
            }
        }

        // 尝试本地编码
        if (hasGarbledChars) {
            QString localResponse = QString::fromLocal8Bit(responseData);
            bool localHasGarbled = localResponse.contains("鐧") || localResponse.contains("鎴") || localResponse.contains("鏂");
            if (!localHasGarbled) {
                emit debugLog("[curl数据包分析] ✅ 本地编码解决了乱码问题");
                response = localResponse;
            }
        }
    } else {
        emit debugLog("[curl数据包分析] ✅ UTF-8编码正常，无需额外处理");
    }

    // curl进程完成
    LOG_INFO(LogCategory::NETWORK, QString("请求成功，响应长度: %1，耗时: %2ms").arg(response.length()).arg(duration.count()));

    // 记录成功统计
    {
        QMutexLocker locker(&m_statsMutex);
        m_performanceStats.curlRequests++;
        m_performanceStats.curlTotalTime += duration.count();
        m_performanceStats.curlAverageTime = m_performanceStats.curlTotalTime / m_performanceStats.curlRequests;

        // 更新性能对比
        if (m_performanceStats.ultraFastRequests > 0 && m_performanceStats.ultraFastAverageTime > 0) {
            m_performanceStats.performanceImprovement =
                ((m_performanceStats.curlAverageTime - m_performanceStats.ultraFastAverageTime) / m_performanceStats.curlAverageTime) * 100.0;
            m_performanceStats.preferredEngine = m_performanceStats.ultraFastAverageTime < m_performanceStats.curlAverageTime ? "UltraFastTLS" : "curl";
        }
    }

    // 最终编码分析（使用之前声明的变量）
    emit debugLog(QString("[curl编码分析] 最终响应长度: %1 字符").arg(response.length()));
    emit debugLog(QString("[curl编码分析] 最终包含乱码字符: %1").arg(hasGarbledChars ? "是" : "否"));

    if (hasGarbledChars) {
        emit debugLog("[curl编码分析] ⚠️ 检测到中文乱码，可能是编码或压缩问题");
        // 尝试显示原始字节的十六进制
        QByteArray rawBytes = response.toUtf8();
        emit debugLog(QString("[curl编码分析] 原始字节前40字符(hex): %1").arg(rawBytes.left(40).toHex()));
    }

    // 调试：显示响应内容（限制长度避免日志过长）
    if (response.length() <= 200) {
        emit debugLog(QString("[TLS伪装] 响应内容: %1").arg(response));
    } else {
        emit debugLog(QString("[TLS伪装] 响应内容(前200字符): %1").arg(response.left(200)));
    }

    // executeCurlRequest 执行完成
    return response;
}

// 主账号专用登录方法（使用TLS指纹伪装）
void OrderAPI::mainAccountLogin(const QString& account, const QString& password, std::function<void(const QString&)> callback)
{
    LOG_INFO(LogCategory::LOGIN, "开始主账号登录: " + account);
    LOG_INFO(LogCategory::NETWORK, QString("当前网络引擎: %1").arg(
        m_networkEngine == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS" : "curl"));
    LOG_DEBUG(LogCategory::LOGIN, "步骤1: 预检验获取LoginID");

    // 默认使用优化后的UltraFastTLS引擎
    LOG_INFO(LogCategory::LOGIN, "使用优化后的UltraFastTLS引擎进行登录");
    LOG_DEBUG(LogCategory::NETWORK, "curl可用，路径: " + m_curlPath);

    // 先进行预检验获取LoginID
    QString preCheckUrl = buildApiUrl(ApiConstants::Actions::USER_TIP_FOR_CHANGE_PASS);
    QStringList preCheckParams;
    preCheckParams << "LoginID=" + account
                   << "TimeStamp=" + QString::number(QDateTime::currentSecsSinceEpoch())
                   << "Ver=1.0"
                   << "AppVer=5.0.6"
                   << "AppOS=WebApp%20IOS"
                   << "AppID=webapp";
    QString preCheckData = preCheckParams.join("&");
    QString preCheckSign = signstr("UserTipForChangePass", preCheckData, "");
    preCheckData += "&Sign=" + preCheckSign;

    LOG_DEBUG(LogCategory::API, "预检验URL: " + preCheckUrl);
    LOG_DEBUG(LogCategory::API, QString("预检验数据长度: %1").arg(preCheckData.length()));
    LOG_INFO(LogCategory::NETWORK, QString("使用 %1 引擎进行预检验")
                  .arg(m_networkEngine == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS" : "curl"));
    QString preCheckResponse = executeNetworkRequest(preCheckUrl, preCheckData, "");

    LOG_DEBUG(LogCategory::API, QString("预检验响应长度: %1").arg(preCheckResponse.length()));
    if (!preCheckResponse.isEmpty()) {
        QString preview = preCheckResponse.length() > 200 ? preCheckResponse.left(200) + "..." : preCheckResponse;
        LOG_DEBUG(LogCategory::API, QString("预检验响应内容: %1").arg(preview));
    } else {
        LOG_ERROR(LogCategory::API, "预检验响应为空");
    }

    // 解析LoginID（保持原有逻辑不变）
    QJsonDocument preCheckDoc = QJsonDocument::fromJson(preCheckResponse.toUtf8());
    QString loginId;
    if (preCheckDoc.isObject()) {
        loginId = preCheckDoc.object().value("LoginID").toString();
        LOG_DEBUG(LogCategory::API, QString("JSON解析成功，LoginID: %1").arg(loginId.isEmpty() ? "空" : loginId));
    } else {
        LOG_ERROR(LogCategory::API, "JSON解析失败");
    }

    if (loginId.isEmpty()) {
        LOG_ERROR(LogCategory::LOGIN, "预检验失败: 无法获取LoginID");
        callback("{\"success\":false,\"message\":\"获取LoginID失败\"}");
        return;
    }

    LOG_INFO(LogCategory::LOGIN, "预检验成功，LoginID: " + loginId);
    LOG_DEBUG(LogCategory::LOGIN, "步骤2: 执行登录请求");

    // 保存登录信息
    m_pendingLoginId = loginId;
    m_pendingPassword = password;
    m_encryptedPayPass = passwordEncrypt(password, loginId);

    // 构建登录请求
    QString encryptedPassword = encryptPassword(password, loginId);
    QString timeStamp = QString::number(QDateTime::currentSecsSinceEpoch());

    QStringList loginParams;
    loginParams << "LoginID=" + loginId
                << "Pass=" + encryptedPassword
                << "OS=WebApp"
                << "verifystr="
                << "HD="
                << "Channels=web"
                << "UserID=0"
                << "TimeStamp=" + timeStamp
                << "Ver=1.0"
                << "AppVer=4.6.4"
                << "AppOS=WebApp%20IOS"
                << "AppID=webapp";

    QString loginData = loginParams.join("&");
    QString loginSign = signstr("GoHome", loginData, "");
    loginData += "&Sign=" + loginSign;

    QString loginUrl = buildApiUrl(ApiConstants::Actions::GO_HOME);
    // 使用当前配置的网络引擎
    LOG_INFO(LogCategory::LOGIN, QString("使用 %1 引擎进行登录")
                  .arg(m_networkEngine == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS" : "curl"));
    QString loginResponse = executeNetworkRequest(loginUrl, loginData, "");

    // 调试：显示登录响应内容
    LOG_DEBUG(LogCategory::LOGIN, QString("登录响应长度: %1").arg(loginResponse.length()));
    if (!loginResponse.isEmpty()) {
        QString preview = loginResponse.length() > 200 ? loginResponse.left(200) + "..." : loginResponse;
        LOG_DEBUG(LogCategory::LOGIN, QString("登录响应内容: %1").arg(preview));
    } else {
        LOG_ERROR(LogCategory::LOGIN, "登录响应为空");
    }

    // 解析登录响应，保存token和userId
    QJsonDocument loginDoc = QJsonDocument::fromJson(loginResponse.toUtf8());
    if (loginDoc.isObject()) {
        QJsonObject obj = loginDoc.object();
        if (obj["Result"].toInt() == 1) {
            m_currentToken = obj["Token"].toString();
            m_currentUserId = QString::number(obj["UserID"].toInt());
            LOG_INFO(LogCategory::LOGIN, "登录成功，已保存Token和UserID");
        } else {
            LOG_ERROR(LogCategory::LOGIN, "登录失败: " + obj["Message"].toString());
        }
    } else {
        LOG_ERROR(LogCategory::LOGIN, "登录响应解析失败");
    }

    callback(loginResponse);
}

// 主账号专用刷新订单方法（使用TLS指纹伪装）
void OrderAPI::mainAccountRefreshOrders(const QString &gameId, const QString &token, const QString &userId,
                                       const QString &priceStr, int focusedFlag)
{
    // mainAccountRefreshOrders 开始执行
    LOG_DEBUG(LogCategory::ORDER, QString("mainAccountRefreshOrders 开始执行 gameId=%1 userId=%2").arg(gameId, userId));
    LOG_INFO(LogCategory::ORDER, QString("刷新订单 gameId=%1 userId=%2").arg(gameId, userId));

    // 使用优化后的UltraFastTLS引擎，性能优异且稳定
    emit debugLog("⚡ [主账号TLS伪装] 使用优化后的UltraFastTLS引擎进行刷新");

    // 构建参数列表 - 完全对齐原有方法的参数
    QList<QPair<QString, QString>> params = {
        {"IsPub", "0"},
        {"GameID", gameId},
        {"ZoneID", "0"},
        {"ServerID", "0"},
        {"SearchStr", ""},
        {"STier", ""},
        {"ETier", ""},
        {"Sort_Str", ""},
        {"PageIndex", "1"},
        {"PageSize", QString::number(m_pageSize)},
        {"Price_Str", priceStr},
        {"PubCancel", "0"},
        {"SettleHour", "0"},
        {"FilterType", "0"},
        {"PGType", "0"},
        {"Focused", QString::number(focusedFlag)},
        {"OrderType", "0"},
        {"PubRecommend", "0"},
        {"Score1", "0"},
        {"Score2", "0"},
        {"UserID", userId},
        {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
        {"Ver", "1.0"},
        {"AppVer", "4.6.4"},
        {"AppOS", "WebApp%20IOS"},
        {"AppID", "webapp"}
    };

    // 添加价格筛选参数
    if (!priceStr.isEmpty()) {
        params.append({"PriceStr", priceStr});
    }

    // 添加关注标志
    if (focusedFlag != -1) {
        params.append({"IsFocused", QString::number(focusedFlag)});
    }

    // 添加token
    if (!token.isEmpty()) {
        params.append({"Token", token});
    }

    // 计算签名
    QString action = "LevelOrderList";
    QString sign = signstr(action, params, token);
    params.append({"Sign", sign});

    // 构建POST数据
    QString postData = buildPostData(params);

    QString url = buildApiUrl(ApiConstants::Actions::LEVEL_ORDER_LIST);
    // 即将执行curl请求
    // 使用当前配置的网络引擎
    LOG_DEBUG(LogCategory::ORDER, QString("使用 %1 引擎刷新订单")
                  .arg(m_networkEngine == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS" : "curl"));
    QString response = executeNetworkRequest(url, postData, "");
    // curl请求完成

    // 解析响应
    // mainAccountRefreshOrders 开始解析响应
    emit debugLog(QString("[调试] mainAccountRefreshOrders 开始解析响应"));
    QByteArray responseData = response.toUtf8();
    parseOrderRefreshResponse(responseData);
    // mainAccountRefreshOrders 执行完成
    emit debugLog(QString("[调试] mainAccountRefreshOrders 执行完成"));
}

// 主账号专用获取用户信息方法（使用TLS指纹伪装）
void OrderAPI::mainAccountGetUserInfo(const QString &token, const QString &userId)
{
    LOG_INFO(LogCategory::API, "获取用户信息 userId: " + userId);

    // 默认使用优化后的UltraFastTLS引擎
    LOG_INFO(LogCategory::API, "使用优化后的UltraFastTLS引擎获取用户信息");

    QList<QPair<QString, QString>> params = {
        {"UserID", userId},
        {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
        {"Ver", "1.0"},
        {"AppVer", "4.6.4"},
        {"AppOS", "WebApp%20IOS"},
        {"AppID", "webapp"}
    };

    if (!token.isEmpty()) {
        params.append({"Token", token});
    }

    QString action = "UserInfoList";
    QString sign = signstr(action, params, token);
    params.append({"Sign", sign});

    QString postData = buildPostData(params);
    QString url = buildApiUrl(ApiConstants::Actions::USER_INFO_LIST);
    // 使用当前配置的网络引擎
    LOG_DEBUG(LogCategory::API, QString("使用 %1 引擎获取用户信息")
                  .arg(m_networkEngine == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS" : "curl"));
    QString response = executeNetworkRequest(url, postData, "");

    // 解析响应
    QByteArray responseData = response.toUtf8();
    parseUserInfoResponse(responseData);
}

// 主账号专用拉黑用户方法（使用TLS指纹伪装）
void OrderAPI::mainAccountAddBlackUser(const QString &blackUserId, const QString &userId, const QString &token)
{
    emit debugLog(QString("[主账号TLS伪装] 拉黑用户 blackUserId=%1").arg(blackUserId));

    // 使用优化后的UltraFastTLS引擎
    emit debugLog("⚡ [主账号TLS伪装] 使用优化后的UltraFastTLS引擎");

    QList<QPair<QString, QString>> params = {
        {"BlackUserID", blackUserId},
        {"UserID", userId},
        {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
        {"Ver", "1.0"},
        {"AppVer", "4.6.4"},
        {"AppOS", "WebApp%20IOS"},
        {"AppID", "webapp"}
    };

    if (!token.isEmpty()) {
        params.append({"Token", token});
    }

    QString action = "AddBlack";
    QString sign = signstr(action, params, token);
    params.append({"Sign", sign});

    QString postData = buildPostData(params);
    QString url = "https://server.dailiantong.com.cn/API/AppService.ashx?Action=AddBlack";
    QString response = executeNetworkRequest(url, postData, "");

    emit debugLog(QString("[主账号TLS伪装] 拉黑用户响应: %1").arg(response.left(100)));
}

// 主账号专用取消拉黑用户方法（使用TLS指纹伪装）
void OrderAPI::mainAccountRemoveBlackUser(const QString &blackUserId, const QString &userId, const QString &token)
{
    emit debugLog(QString("[主账号TLS伪装] 取消拉黑用户 blackUserId=%1").arg(blackUserId));

    // 使用优化后的UltraFastTLS引擎
    emit debugLog("⚡ [主账号TLS伪装] 使用优化后的UltraFastTLS引擎");

    QList<QPair<QString, QString>> params = {
        {"BlackUserID", blackUserId},
        {"UserID", userId},
        {"TimeStamp", QString::number(QDateTime::currentSecsSinceEpoch())},
        {"Ver", "1.0"},
        {"AppVer", "4.6.4"},
        {"AppOS", "WebApp%20IOS"},
        {"AppID", "webapp"}
    };

    if (!token.isEmpty()) {
        params.append({"Token", token});
    }

    QString action = "RemoveMember";
    QString sign = signstr(action, params, token);
    params.append({"Sign", sign});

    QString postData = buildPostData(params);
    QString url = "https://server.dailiantong.com.cn/API/AppService.ashx?Action=RemoveMember";
    QString response = executeNetworkRequest(url, postData, "");

    emit debugLog(QString("[主账号TLS伪装] 取消拉黑用户响应: %1").arg(response.left(100)));
}

// 主账号专用关注用户方法（使用TLS指纹伪装）
void OrderAPI::mainAccountFocusUser(const QString &focusUserId, const QString &serialNo, const QString &userId)
{
    emit debugLog(QString("[主账号TLS伪装] *** 关注用户方法被调用 *** focusUserId=%1").arg(focusUserId));

    // 使用优化后的UltraFastTLS引擎
    emit debugLog("⚡ [主账号TLS伪装] 使用优化后的UltraFastTLS引擎");

    QString timestamp = QString::number(QDateTime::currentSecsSinceEpoch());
    QString channel = "1";
    const QString secretKey = "9c7b9399680658d308691f2acad58c0a";
    QString signSource = userId + focusUserId + channel + serialNo + timestamp + secretKey;
    QString sign = md5Encrypt(signSource);

    // 构建URL查询参数 - 使用与原有方法相同的端点
    QString url = "https://quickorder.dailiantong.com.cn/api/revision/FocusNew";
    QString queryParams = QString("UserID=%1&FocusUserID=%2&Channel=%3&SerialNo=%4&timeStamp=%5&Sign=%6")
                         .arg(userId, focusUserId, channel, serialNo, timestamp, sign);

    QString fullUrl = url + "?" + queryParams;
    QString response = executeNetworkRequest(fullUrl, "", "");

    emit debugLog(QString("[主账号TLS伪装] 关注用户响应: %1").arg(response.left(100)));

    // 解析响应并发出信号
    QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8());
    if (doc.isObject()) {
        QJsonObject obj = doc.object();
        bool success = obj["Result"].toInt() == 1;
        QString message = obj["Message"].toString();
        emit focusUserResult(success, message, focusUserId);
    } else {
        emit focusUserResult(false, "响应解析失败", focusUserId);
    }
}

// 主账号专用取消关注用户方法（使用TLS指纹伪装）
void OrderAPI::mainAccountCancelFocusUser(const QString &focusUserId, const QString &userId)
{
    emit debugLog(QString("[主账号TLS伪装] 取消关注用户 focusUserId=%1").arg(focusUserId));

    // 使用优化后的UltraFastTLS引擎
    emit debugLog("⚡ [主账号TLS伪装] 使用优化后的UltraFastTLS引擎");

    QString timestamp = QString::number(QDateTime::currentSecsSinceEpoch());
    const QString secretKey = "9c7b9399680658d308691f2acad58c0a";
    QString signSource = userId + focusUserId + timestamp + secretKey;
    QString sign = md5Encrypt(signSource);

    // 构建URL查询参数
    QString url = "https://quickorder.dailiantong.com.cn/api/revision/CancelFocus";
    QString queryParams = QString("UserID=%1&FocusUserID=%2&timeStamp=%3&Sign=%4")
                         .arg(userId, focusUserId, timestamp, sign);

    QString fullUrl = url + "?" + queryParams;
    QString response = executeNetworkRequest(fullUrl, "", "");

    emit debugLog(QString("[主账号TLS伪装] 取消关注用户响应: %1").arg(response.left(100)));

    // 解析响应并发出信号
    QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8());
    if (doc.isObject()) {
        QJsonObject obj = doc.object();
        bool success = obj["Result"].toInt() == 1;
        QString message = obj["Message"].toString();
        emit cancelFocusUserResult(success, message, focusUserId);
    } else {
        emit cancelFocusUserResult(false, "响应解析失败", focusUserId);
    }
}

// 主账号专用接单方法（使用TLS指纹伪装）
void OrderAPI::mainAccountAcceptOrder(const QString &orderId, const QString &stamp, const QString &token,
                                     const QString &userId, const QString &payPassword, const QString &loginId)
{
    LOG_INFO(LogCategory::ORDER, QString("接单方法被调用 orderId=%1").arg(orderId));

    // 接单参数详情

    emit debugLog(QString("[主账号TLS伪装] Token详情: 长度=%1, 前20字符=%2").arg(token.length()).arg(token.left(20)));
    emit debugLog(QString("[主账号TLS伪装] 其他参数: userId=%1, loginId=%2").arg(userId, loginId));

    // 使用优化后的UltraFastTLS引擎进行接单
    LOG_INFO(LogCategory::ORDER, "使用优化后的UltraFastTLS引擎进行接单");

    // 构建参数 - 对齐原有方法的参数格式
    QString timestamp = QString::number(QDateTime::currentSecsSinceEpoch());
    QString encryptedPayPass = passwordEncrypt(payPassword, loginId);

    // 构建请求参数

    // 使用与原有方法完全相同的参数格式（添加所有缺少的参数）
    QString postData = QString("ODSerialNo=%1&Stamp=%2&PayPass=%3&Insurance=0&MaxClaimAmount=0&VisitType=0&NonceStr=&NoEnsure=0&IsWx=0&SourceType=0&UserID=%4&TimeStamp=%5&Ver=1.0&AppVer=5.0.6&AppOS=WebApp%20IOS&AppID=webapp")
                      .arg(orderId, stamp, encryptedPayPass, userId, timestamp);

    // 修复后的完整postData

    // 添加token
    if (!token.isEmpty()) {
        postData += "&Token=" + token;
        // 添加Token后postData
    } else {
        // 警告: Token为空
        emit debugLog("[主账号TLS伪装] 警告: Token为空！");
    }

    // 计算签名 - 使用与原有方法相同的签名算法
    QString sign = signEncrypt("NewLevelOrderAccept", postData, token);
    postData += "&Sign=" + sign;

    // 最终postData和签名

    emit debugLog(QString("[主账号TLS伪装] 请求数据长度: %1, 签名: %2").arg(postData.length()).arg(sign.left(20)));

    QString url = buildApiUrl(ApiConstants::Actions::NEW_LEVEL_ORDER_ACCEPT);
    QString response = executeNetworkRequest(url, postData, "");

    // 解析响应并发送信号
    // 接单响应原始数据（保持不变，不影响解析）
    LOG_DEBUG(LogCategory::ORDER, QString("接单响应原始数据: %1").arg(response));

    QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8());
    if (doc.isObject()) {
        QJsonObject obj = doc.object();
        int resultCode = obj["Result"].toInt();
        QString resultStr = obj["Result"].toString();
        QString message = obj["Message"].toString();
        QString err = obj["Err"].toString();

        // 解析结果处理
        // 解析结果

        bool success = resultCode == 1;
        QString finalMessage = message.isEmpty() ? err : message;

        emit orderAcceptResult(success, finalMessage);
        LOG_INFO(LogCategory::ORDER, QString("接单响应: %1 - %2 (ResultCode=%3)").arg(success ? "成功" : "失败", finalMessage).arg(resultCode));
    } else {
        // JSON解析失败
        emit orderAcceptResult(false, "响应解析失败");
        LOG_ERROR(LogCategory::ORDER, "接单响应解析失败");
    }
}

// 自动查找curl路径
void OrderAPI::findCurlPath()
{
    // 可能的curl路径列表
    QStringList possiblePaths = {
        "C:/Users/<USER>/Documents/curl-8.15.0_4-win64-mingw/curl-8.15.0_4-win64-mingw/bin/curl.exe",  // 实际路径
        "C:/Users/<USER>/Documents/curl-8.15.0_4-win64-mingw/bin/curl.exe",
        "C:/Users/<USER>/Documents/curl-8.15.0_4-win64-mingw/curl.exe",
        "C:/curl/bin/curl.exe",
        "C:/curl/curl.exe",
        "curl.exe"  // 系统PATH中的curl
    };

    // 检查每个可能的路径
    for (const QString &path : possiblePaths) {
        QFileInfo fileInfo(path);
        if (fileInfo.exists() && fileInfo.isExecutable()) {
            m_curlPath = path;
            LOG_INFO(LogCategory::SYSTEM, QString("找到curl: %1").arg(path));
            return;
        }
    }

    // 如果都没找到，尝试在系统PATH中查找
    QProcess process;
    process.start("where", QStringList() << "curl.exe");
    if (process.waitForFinished(3000) && process.exitCode() == 0) {
        QString output = process.readAllStandardOutput().trimmed();
        QStringList lines = output.split('\n');
        if (!lines.isEmpty()) {
            m_curlPath = lines.first().trimmed();
            LOG_INFO(LogCategory::SYSTEM, QString("在PATH中找到curl: %1").arg(m_curlPath));
            return;
        }
    }

    LOG_WARNING(LogCategory::SYSTEM, "未找到curl，TLS伪装功能将不可用");
    m_curlPath = "";
}

// 统一的网络请求接口 - 已简化
QString OrderAPI::executeNetworkRequest(const QString &url, const QString &postData, const QString &headers)
{
    // 🚀 100%使用UltraFastTLS引擎 - 永不使用curl
    emit debugLog("⚡ [网络引擎] 100%使用UltraFastTLS引擎，永不回退到curl");

    // 确保UltraFastTLS已初始化
    if (!m_ultraFastTLS) {
        emit debugLog("🔧 [网络引擎] UltraFastTLS未初始化，立即初始化");
        m_ultraFastTLS = new UltraFastTLS(this);
        m_ultraFastTLS->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);
        m_ultraFastTLS->setQuietMode(true);
        emit debugLog("✅ [网络引擎] UltraFastTLS初始化完成");
    }

    // 100%使用UltraFastTLS，永不回退
    return executeUltraFastTLSRequest(url, postData, headers);

    // 🗑️ 已删除所有curl回退逻辑 - 100%使用UltraFastTLS
}

// UltraFastTLS 网络请求实现 - 已修复卡死问题
QString OrderAPI::executeUltraFastTLSRequest(const QString &url, const QString &postData, const QString &headers)
{
    emit debugLog("=== [UltraFastTLS请求] 真正开始使用UltraFastTLS ===");

    if (!m_ultraFastTLS) {
        emit debugLog("[UltraFastTLS] ❌ UltraFastTLS 未初始化，立即创建");
        m_ultraFastTLS = new UltraFastTLS(this);
        m_ultraFastTLS->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);
        m_ultraFastTLS->setQuietMode(true);
        emit debugLog("[UltraFastTLS] ✅ UltraFastTLS对象创建完成");
    }

    emit debugLog("[UltraFastTLS] ✅ UltraFastTLS对象存在，开始执行请求");

    emit debugLog(QString("[UltraFastTLS] 🚀 开始执行请求: %1").arg(url));
    emit debugLog(QString("[UltraFastTLS] 📝 POST数据长度: %1").arg(postData.length()));
    emit debugLog(QString("[UltraFastTLS] 📋 请求头: %1").arg(headers.isEmpty() ? "无" : "有"));

    // 显示POST数据的前100个字符用于调试
    if (!postData.isEmpty()) {
        QString preview = postData.length() > 100 ? postData.left(100) + "..." : postData;
        emit debugLog(QString("[UltraFastTLS] 📄 POST数据预览: %1").arg(preview));
    }

    // 🗑️ 删除初始化检查 - 永不回退到curl
    emit debugLog("[UltraFastTLS] 🚀 跳过初始化检查，直接使用UltraFastTLS");

    // 记录开始时间
    auto startTime = std::chrono::high_resolution_clock::now();

    // 执行请求（已修复：非阻塞、超时机制、避免死锁）
    emit debugLog("[UltraFastTLS] 🔄 调用修复后的 executeRequest...");
    emit debugLog("[UltraFastTLS] ⏱️ 开始计时...");
    emit debugLog("[UltraFastTLS] 🎯 即将进入UltraFastTLS核心代码");

    QString response;
    // 🗑️ 删除异常处理中的curl回退 - 永不回退
    response = m_ultraFastTLS->executeRequest(url, postData);
    emit debugLog("[UltraFastTLS] ✅ executeRequest调用完成");

    // 记录结束时间
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    emit debugLog(QString("[UltraFastTLS] ⏱️ 请求完成，耗时: %1ms").arg(duration.count()));

    if (!response.isEmpty()) {
        emit debugLog(QString("[UltraFastTLS] ✅ 请求成功，响应长度: %1，耗时: %2ms")
                     .arg(response.length())
                     .arg(duration.count()));

        // 记录成功统计
        {
            QMutexLocker locker(&m_statsMutex);
            m_performanceStats.ultraFastRequests++;
            m_performanceStats.ultraFastTotalTime += duration.count();
            m_performanceStats.ultraFastAverageTime = m_performanceStats.ultraFastTotalTime / m_performanceStats.ultraFastRequests;
        }

        // 明确标识使用UltraFastTLS引擎
        emit debugLog("⚡ [UltraFastTLS引擎] 响应处理完成");
        emit debugLog(QString("⚡ [UltraFastTLS引擎] 最终响应长度: %1 字符").arg(response.length()));

        // 检查是否包含中文乱码字符
        bool hasGarbledChars = response.contains("鐧") || response.contains("鎴") || response.contains("鏂");
        emit debugLog(QString("⚡ [UltraFastTLS引擎] 包含乱码字符: %1").arg(hasGarbledChars ? "是" : "否"));

        if (hasGarbledChars) {
            emit debugLog("⚡ [UltraFastTLS引擎] ⚠️ 检测到中文乱码，可能是编码或压缩问题");
            // 尝试显示原始字节的十六进制
            QByteArray rawBytes = response.toUtf8();
            emit debugLog(QString("⚡ [UltraFastTLS引擎] 原始字节前40字符(hex): %1").arg(rawBytes.left(40).toHex()));
        }

        return response;
    } else {
        emit debugLog(QString("[UltraFastTLS] ❌ 请求失败，耗时: %1ms，回退到 curl")
                     .arg(duration.count()));

        // 记录失败统计
        {
            QMutexLocker locker(&m_statsMutex);
            m_performanceStats.ultraFastRequests++;
            m_performanceStats.ultraFastFailures++;
            m_performanceStats.ultraFastTotalTime += duration.count();
            m_performanceStats.ultraFastAverageTime = m_performanceStats.ultraFastTotalTime / m_performanceStats.ultraFastRequests;
        }

        // 🗑️ 删除curl回退 - 永不回退，直接返回空响应
        emit debugLog("[UltraFastTLS] 🚀 永不回退到curl，返回空响应");
        return QString();
    }
}

// 重置性能统计
void OrderAPI::resetPerformanceStats()
{
    QMutexLocker locker(&m_statsMutex);
    m_performanceStats = ::NetworkPerformanceStats();
    emit debugLog("[性能监控] 性能统计已重置");
}

// 获取性能报告
QString OrderAPI::getPerformanceReport() const
{
    QMutexLocker locker(&m_statsMutex);

    QString report = "\n========== 网络性能监控报告 ==========\n";

    // curl 统计
    report += QString("📊 curl 统计:\n");
    report += QString("  请求次数: %1\n").arg(m_performanceStats.curlRequests);
    report += QString("  失败次数: %1\n").arg(m_performanceStats.curlFailures);
    report += QString("  成功率: %1%\n").arg(m_performanceStats.curlRequests > 0 ?
        (double)(m_performanceStats.curlRequests - m_performanceStats.curlFailures) / m_performanceStats.curlRequests * 100.0 : 0.0, 0, 'f', 1);
    report += QString("  平均响应时间: %1ms\n").arg(m_performanceStats.curlAverageTime, 0, 'f', 1);
    report += QString("  总耗时: %1ms\n").arg(m_performanceStats.curlTotalTime, 0, 'f', 1);

    report += "\n";

    // UltraFastTLS 统计
    report += QString("🚀 UltraFastTLS 统计:\n");
    report += QString("  请求次数: %1\n").arg(m_performanceStats.ultraFastRequests);
    report += QString("  失败次数: %1\n").arg(m_performanceStats.ultraFastFailures);
    report += QString("  成功率: %1%\n").arg(m_performanceStats.ultraFastRequests > 0 ?
        (double)(m_performanceStats.ultraFastRequests - m_performanceStats.ultraFastFailures) / m_performanceStats.ultraFastRequests * 100.0 : 0.0, 0, 'f', 1);
    report += QString("  平均响应时间: %1ms\n").arg(m_performanceStats.ultraFastAverageTime, 0, 'f', 1);
    report += QString("  总耗时: %1ms\n").arg(m_performanceStats.ultraFastTotalTime, 0, 'f', 1);

    report += "\n";

    // 性能对比
    if (m_performanceStats.curlRequests > 0 && m_performanceStats.ultraFastRequests > 0) {
        report += QString("⚡ 性能对比:\n");
        report += QString("  性能提升: %1%\n").arg(m_performanceStats.performanceImprovement, 0, 'f', 1);
        report += QString("  推荐引擎: %1\n").arg(m_performanceStats.preferredEngine);

        if (m_performanceStats.performanceImprovement > 0) {
            report += QString("  🎯 UltraFastTLS 比 curl 快 %1%\n").arg(m_performanceStats.performanceImprovement, 0, 'f', 1);
        } else {
            report += QString("  ⚠️ curl 比 UltraFastTLS 快 %1%\n").arg(-m_performanceStats.performanceImprovement, 0, 'f', 1);
        }
    } else {
        report += QString("⚠️ 性能对比: 数据不足，需要两种引擎都有请求记录\n");
    }

    report += "=====================================\n";

    return report;
}

// 获取当前 TLS 指纹信息
TLSFingerprintInfo OrderAPI::getCurrentFingerprint() const
{
    TLSFingerprintInfo info;

    // 夸克浏览器的标准指纹信息
    const QString QUARK_JA3 = "7831fa6465c3511f38bd96c933a8459e";  // 夸克浏览器 7.14.5.880 的 JA3
    const QString QUARK_USER_AGENT = "Mozilla/5.0 (Linux; U; Android 15; zh-CN; V2307A Build/AP3A.240905.015.A1) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.80 Quark/7.14.5.880 Mobile Safari/537.36";

    // 夸克浏览器Android 14的标准指纹信息  
    const QString QUARK_ANDROID14_JA3 = "4c87eb5f587bf477c55677398fa9fbe2";  // 夸克浏览器 Android 14 的真实 JA3
    const QString QUARK_ANDROID14_USER_AGENT = "Mozilla/5.0 (Linux; U; Android 14; zh-CN; V2055A Build/UP1A.231005.007) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/100.0.4896.58 Quark/7.14.5.880 Mobile Safari/537.36";

    // 微信浏览器的标准指纹信息
    const QString WECHAT_JA3 = "79e2c4451f525f5cfc10860a9eb180aa";  // 微信 8.0.61.2880 的真实 JA3
    const QString WECHAT_USER_AGENT = "Mozilla/5.0 (Linux; Android 15; V2307A Build/AP3A.240905.015.A1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.136 Mobile Safari/537.36 XWEB/1340153 MMWEBSDK/******** MMWEBID/7101 MicroMessenger/8.0.61.2880(0x28003D57) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64";

    if (m_networkEngine == NetworkEngine::ULTRA_FAST_TLS && m_ultraFastTLS) {
        // 从 UltraFastTLS 获取指纹信息
        auto stats = m_ultraFastTLS->getStats();
        info.ja3Hash = QUARK_JA3;  // UltraFastTLS 配置的夸克指纹
        info.ja4Hash = "t13d1516h2_8daaf6152771_02713d6af862";  // 对应的 JA4
        info.userAgent = QUARK_USER_AGENT;
        info.tlsVersion = "TLS 1.3";
        info.cipherSuites = {
            "TLS_AES_128_GCM_SHA256",
            "TLS_AES_256_GCM_SHA384",
            "TLS_CHACHA20_POLY1305_SHA256",
            "ECDHE-ECDSA-AES128-GCM-SHA256",
            "ECDHE-RSA-AES128-GCM-SHA256"
        };
        info.extensions = {
            "server_name",
            "extended_master_secret",
            "renegotiation_info",
            "supported_groups",
            "ec_point_formats",
            "signature_algorithms"
        };
        info.isQuarkCompatible = true;
        info.matchScore = 95.0;  // UltraFastTLS 高匹配度
    } else {
        // curl 模式的指纹信息
        info.ja3Hash = "未知 (curl模式)";
        info.ja4Hash = "未知 (curl模式)";
        info.userAgent = "curl/8.15.0";  // curl 的默认 User-Agent
        info.tlsVersion = "TLS 1.2";
        info.cipherSuites = {"基础密码套件 (curl默认)"};
        info.extensions = {"基础扩展 (curl默认)"};
        info.isQuarkCompatible = false;
        info.matchScore = 30.0;  // curl 低匹配度
    }

    return info;
}

// 验证夸克浏览器指纹
bool OrderAPI::verifyQuarkFingerprint() const
{
    auto fingerprint = getCurrentFingerprint();

    // 检查关键指标
    bool ja3Match = fingerprint.ja3Hash.contains("7831fa6465c3511f38bd96c933a8459e");
    bool tlsVersionOk = fingerprint.tlsVersion.contains("TLS 1.3") || fingerprint.tlsVersion.contains("TLS 1.2");
    bool cipherSuitesOk = fingerprint.cipherSuites.size() >= 3;
    bool extensionsOk = fingerprint.extensions.size() >= 4;

    return fingerprint.isQuarkCompatible && ja3Match && tlsVersionOk && cipherSuitesOk && extensionsOk;
}

// 获取指纹验证报告
QString OrderAPI::getFingerprintReport() const
{
    auto fingerprint = getCurrentFingerprint();

    QString report = "\n========== TLS 指纹验证报告 ==========\n";

    report += QString("🔍 当前网络引擎: %1\n").arg(
        m_networkEngine == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS (优化)" : "curl (基础)"
    );

    report += QString("📊 指纹信息:\n");
    report += QString("  JA3 哈希: %1\n").arg(fingerprint.ja3Hash);
    report += QString("  JA4 哈希: %1\n").arg(fingerprint.ja4Hash);
    report += QString("  User-Agent: %1\n").arg(fingerprint.userAgent.left(60) + "...");
    report += QString("  TLS 版本: %1\n").arg(fingerprint.tlsVersion);
    report += QString("  密码套件数量: %1\n").arg(fingerprint.cipherSuites.size());
    report += QString("  扩展数量: %1\n").arg(fingerprint.extensions.size());

    report += "\n";

    // 夸克浏览器兼容性
    report += QString("🎯 夸克浏览器兼容性:\n");
    report += QString("  兼容状态: %1\n").arg(fingerprint.isQuarkCompatible ? "✅ 兼容" : "❌ 不兼容");
    report += QString("  匹配度评分: %1/100\n").arg(fingerprint.matchScore, 0, 'f', 1);

    if (fingerprint.matchScore >= 90) {
        report += "  🏆 优秀 - 完美匹配夸克浏览器指纹\n";
    } else if (fingerprint.matchScore >= 70) {
        report += "  ✅ 良好 - 较好匹配夸克浏览器指纹\n";
    } else if (fingerprint.matchScore >= 50) {
        report += "  ⚠️ 一般 - 部分匹配夸克浏览器指纹\n";
    } else {
        report += "  ❌ 较差 - 与夸克浏览器指纹差异较大\n";
    }

    report += "\n";

    // 建议
    report += QString("💡 优化建议:\n");
    if (m_networkEngine == NetworkEngine::CURL) {
        report += "  🚀 建议切换到 UltraFastTLS 引擎以获得更好的指纹匹配\n";
        report += "  📈 预期性能提升: 3-5倍\n";
        report += "  🎯 预期匹配度提升: 65分\n";
    } else {
        report += "  ✅ 当前已使用最优配置\n";
        report += "  🔐 夸克浏览器指纹伪装效果优秀\n";
    }

    report += "=====================================\n";

    return report;
}

// ========== 主账号指纹选择功能 ==========

void OrderAPI::setMainAccountFingerprint(MainAccountFingerprint fingerprint)
{
    m_mainAccountFingerprint = fingerprint;
    emit debugLog(QString("🎭 主账号指纹已切换为: %1")
                  .arg(fingerprint == QUARK_FINGERPRINT ? "夸克浏览器" : "微信浏览器"));
}

OrderAPI::MainAccountFingerprint OrderAPI::getMainAccountFingerprint() const
{
    return m_mainAccountFingerprint;
}

// 生成UltraFastTLS调试报告
QString OrderAPI::generateUltraFastTLSDebugReport()
{
    QString report = "\n========== UltraFastTLS 调试报告 ==========\n";

    // 基本状态
    report += QString("🔧 当前网络引擎: %1\n").arg(
        m_networkEngine == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS (优化)" : "curl (基础)"
    );

    // UltraFastTLS对象状态
    if (m_ultraFastTLS) {
        report += "✅ UltraFastTLS 对象已创建\n";

        // 尝试简单的初始化测试
        report += "🔍 测试UltraFastTLS初始化...\n";
        if (m_ultraFastTLS->initialize()) {
            report += "✅ UltraFastTLS 初始化成功\n";
        } else {
            report += "❌ UltraFastTLS 初始化失败\n";
        }

        // 尝试简单的请求测试
        report += "🔍 测试简单HTTP请求...\n";
        QString testUrl = "https://www.baidu.com";
        emit debugLog("[UltraFastTLS调试] 开始测试请求到百度");
        QString testResponse = m_ultraFastTLS->executeRequest(testUrl);

        if (!testResponse.isEmpty()) {
            report += QString("✅ 测试请求成功，响应长度: %1\n").arg(testResponse.length());
            emit debugLog(QString("[UltraFastTLS调试] 测试成功，响应长度: %1").arg(testResponse.length()));
        } else {
            report += "❌ 测试请求失败\n";
            emit debugLog("[UltraFastTLS调试] 测试失败");
        }

    } else {
        report += "❌ UltraFastTLS 对象未创建\n";
    }

    // 性能统计
    {
        QMutexLocker locker(&m_statsMutex);
        report += "\n📊 性能统计:\n";
        report += QString("  UltraFastTLS 请求次数: %1\n").arg(m_performanceStats.ultraFastRequests);
        report += QString("  UltraFastTLS 失败次数: %1\n").arg(m_performanceStats.ultraFastFailures);
        report += QString("  UltraFastTLS 成功率: %1%\n").arg(
            m_performanceStats.ultraFastRequests > 0 ?
            ((m_performanceStats.ultraFastRequests - m_performanceStats.ultraFastFailures) * 100.0 / m_performanceStats.ultraFastRequests) : 0.0, 0, 'f', 1);
        report += QString("  UltraFastTLS 平均耗时: %1ms\n").arg(m_performanceStats.ultraFastAverageTime, 0, 'f', 1);
    }

    // 建议
    report += "\n💡 调试建议:\n";
    if (m_networkEngine == NetworkEngine::ULTRA_FAST_TLS) {
        if (m_performanceStats.ultraFastFailures > 0) {
            report += "  🔧 UltraFastTLS 有失败记录，建议检查:\n";
            report += "     - OpenSSL 版本兼容性\n";
            report += "     - TLS 握手配置\n";
            report += "     - 网络连接权限\n";
            report += "     - 防火墙设置\n";
        } else {
            report += "  ✅ UltraFastTLS 运行正常\n";
        }
    } else {
        report += "  ℹ️ 当前使用 curl 引擎，UltraFastTLS 未激活\n";
    }

    report += "\n==========================================\n";

    return report;
}

// 测试UltraFastTLS功能
void OrderAPI::testUltraFastTLS()
{
    emit debugLog("🧪 ========== UltraFastTLS 测试开始 ==========");

    if (!m_ultraFastTLS) {
        emit debugLog("❌ UltraFastTLS 对象未创建");
        return;
    }

    // 测试1: 初始化测试
    emit debugLog("🔧 测试1: UltraFastTLS 初始化");
    if (m_ultraFastTLS->initialize()) {
        emit debugLog("✅ 初始化成功");
    } else {
        emit debugLog("❌ 初始化失败");
        return;
    }

    // 测试2: 简单HTTP请求
    emit debugLog("🌐 测试2: 简单HTTP请求");
    QString testUrl = "https://www.baidu.com";
    emit debugLog(QString("📡 请求URL: %1").arg(testUrl));

    QString response = m_ultraFastTLS->executeRequest(testUrl);

    if (!response.isEmpty()) {
        emit debugLog(QString("✅ 请求成功，响应长度: %1").arg(response.length()));
        QString preview = response.length() > 100 ? response.left(100) + "..." : response;
        emit debugLog(QString("📄 响应预览: %1").arg(preview));
    } else {
        emit debugLog("❌ 请求失败");
    }

    // 测试3: HTTPS请求到目标服务器
    emit debugLog("🎯 测试3: 目标服务器HTTPS请求");
    QString targetUrl = "https://server.dailiantong.com.cn";
    emit debugLog(QString("📡 请求URL: %1").arg(targetUrl));

    QString targetResponse = m_ultraFastTLS->executeRequest(targetUrl);

    if (!targetResponse.isEmpty()) {
        emit debugLog(QString("✅ 目标服务器请求成功，响应长度: %1").arg(targetResponse.length()));
    } else {
        emit debugLog("❌ 目标服务器请求失败");
    }

    emit debugLog("🧪 ========== UltraFastTLS 测试结束 ==========");
}

// ============ 新网络引擎架构实现 ============
// 暂时注释掉整个新架构实现，避免编译错误

/*
void OrderAPI::initializeNewNetworkEngines()
{
    emit debugLog("[新架构] 🚀 初始化网络引擎管理器...");

    // 创建网络引擎管理器
    m_engineManager = new NetworkEngineManager(this);

    // 连接调试信号
    connect(m_engineManager, &NetworkEngineManager::debugMessage,
            this, [this](const QString& msg) {
                // 引擎管理器调试信息
            });

    connect(m_engineManager, &NetworkEngineManager::engineChanged,
            this, [this](const QString& engineName) {
                emit debugLog(QString("[新架构] 🔄 引擎切换到: %1").arg(engineName));
            });

    // 创建并添加UltraFastTLS引擎
    UltraFastTLSEngine* ultraEngine = new UltraFastTLSEngine(this);
    if (ultraEngine->initialize()) {
        m_engineManager->addEngine(ultraEngine, 100); // 高优先级
        emit debugLog("[新架构] ✅ UltraFastTLS引擎已添加");
    } else {
        emit debugLog("[新架构] ❌ UltraFastTLS引擎初始化失败");
        delete ultraEngine;
    }

    // 创建并添加Curl引擎
    CurlEngine* curlEngine = new CurlEngine(this);
    if (curlEngine->initialize()) {
        m_engineManager->addEngine(curlEngine, 50); // 低优先级
        emit debugLog("[新架构] ✅ Curl引擎已添加");
    } else {
        emit debugLog("[新架构] ❌ Curl引擎初始化失败");
        delete curlEngine;
    }

    // 设置首选引擎
    m_engineManager->setPreferredEngine("UltraFastTLS");

    emit debugLog("[新架构] 🎯 网络引擎管理器初始化完成");
}

void OrderAPI::cleanupNewNetworkEngines()
{
    if (m_engineManager) {
        emit debugLog("[新架构] 🧹 清理网络引擎管理器...");
        delete m_engineManager;
        m_engineManager = nullptr;
    }
}

QString OrderAPI::executeRequestWithNewEngine(const QString& url, const QString& postData)
{
    if (!m_engineManager) {
        emit debugLog("[新架构] ❌ 网络引擎管理器未初始化");
        return QString();
    }

    NetworkEngine::RequestParams params(url, postData);
    params.timeoutMs = NETWORK_CONFIG.timeout;  // 使用新配置系统

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("执行网络请求: %1 (超时: %2ms)").arg(url).arg(params.timeoutMs));

    NetworkEngine::RequestResult result = m_engineManager->executeRequest(params);

    if (result.success) {
        emit debugLog(QString("[新架构] ✅ 请求成功，响应长度: %1，耗时: %2ms")
                     .arg(result.response.length()).arg(result.elapsedMs));
        return result.response;
    } else {
        emit debugLog(QString("[新架构] ❌ 请求失败，耗时: %1ms，错误: %2")
                     .arg(result.elapsedMs).arg(result.errorMessage));
        return QString();
    }
}
*/

