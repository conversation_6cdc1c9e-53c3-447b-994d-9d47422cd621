#include "orderapi.h"
#include "../utils/simple_logger.h"
#include "api_constants.h"
#include "../../src/config/app_config.h"
#include "../../src/core/utils/logger.h"
#include "../network/ultrafasttls.h"
#include "../network/ultrafasttls_debug_monitor.h"
#include <QNetworkRequest>
#include <QNetworkProxy>
#include <QJsonParseError>
#include <QDebug>
#include <QDateTime>
#include <QThread>
#include <QCryptographicHash>
#include <QUrl>
#include <QNetworkAccessManager>
#include <chrono>
#include <QMutex>
#include <QMutexLocker>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <functional>
#include <QTimer>
#include <QSslError>
#include <QJsonArray>
#include <QtConcurrent>
#include <QProcess>
#include <QStandardPaths>
#include <QFileInfo>

// 直接包含zlib的头文件
#include "C:/Libraries/zlib131/zlib-1.3.1/zlib.h"

// 全局直连最近一次网络活动时间（ms）
static qint64 s_lastDirectActivity = 0;

QHash<QString, QNetworkAccessManager*> OrderAPI::s_managerPool; // 定义静态成员
qint64 OrderAPI::s_lastDirectActivity = 0; // 定义静态成员

QNetworkAccessManager* OrderAPI::managerForProxy(const QString &host, int port, const QString &type,
                                                 const QString &user, const QString &pass)
{
    QString key;
    if (host.isEmpty() || port == 0) {
        key = "direct";
    } else {
        if (!user.isEmpty()) {
            key = QString("%1://%2@%3:%4").arg(type.toLower(), user, host).arg(port);
        } else {
            key = QString("%1://%2:%3").arg(type.toLower(), host).arg(port);
        }
    }
    if (!s_managerPool.contains(key)) {
        auto *mgr = new QNetworkAccessManager;
        if (key != "direct") {
            QNetworkProxy proxy;
            proxy.setType(type.toLower() == "socks5" ? QNetworkProxy::Socks5Proxy : QNetworkProxy::HttpProxy);
            proxy.setHostName(host);
            proxy.setPort(port);
            if (!user.isEmpty() && !pass.isEmpty()) {
                proxy.setUser(user);
                proxy.setPassword(pass);
            }
            mgr->setProxy(proxy);
        }
        s_managerPool.insert(key, mgr);
    }
    return s_managerPool.value(key);
}

OrderAPI::OrderAPI(QObject *parent)
    : QObject(parent)
    , m_keepAliveTimer(nullptr)
    , m_acceptPreheater(nullptr)
    , m_networkManager(nullptr)
    , m_acceptManager(nullptr)
    , m_currentReply(nullptr)
    , m_currentToken("")
    , m_currentUserId("")
    , m_pendingPassword("")
    , m_pendingLoginId("")
    , m_encryptedPayPass("")
    , m_pendingProxyPort(0)
    , m_curlPath("")
{
    // 新架构集成：使用统一配置和日志
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "OrderAPI 初始化开始");
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("网络超时配置: %1ms").arg(NETWORK_CONFIG.timeout));
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("用户代理: %1").arg(NETWORK_CONFIG.userAgent));

    // 自动查找curl路径
    findCurlPath();

    // 临时禁用UltraFastTLS，强制使用稳定的curl
    m_networkEngine = NetworkEngine::CURL;
    emit debugLog("[代码整理] 临时禁用UltraFastTLS，使用稳定的curl引擎");

    // 初始化UltraFastTLS（安全模式）
    emit debugLog("[OrderAPI] 🔍 开始创建UltraFastTLS对象...");
    try {
        m_ultraFastTLS = new UltraFastTLS(this);
        emit debugLog("[OrderAPI] 🔍 UltraFastTLS对象创建成功");
        m_ultraFastTLS->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);
        m_debugMonitor = new UltraFastTLSDebugMonitor(this);
        m_ultraFastTLS->setDebugMonitor(m_debugMonitor);

        connect(m_ultraFastTLS, &UltraFastTLS::debugLog, this, [this](const QString& msg) {
            emit debugLog(msg);
        });

        // 尝试初始化UltraFastTLS
        emit debugLog("[OrderAPI] 🔍 准备调用UltraFastTLS->initialize()...");
        if (m_ultraFastTLS->initialize()) {
            // UltraFastTLS 初始化成功，使用优化模式
            emit debugLog("[OrderAPI] 🔍 UltraFastTLS初始化成功，使用优化模式");
            m_networkEngine = NetworkEngine::ULTRA_FAST_TLS;
            emit debugLog("[OrderAPI] 🔄 网络引擎已设置为ULTRA_FAST_TLS");
        } else {
            // UltraFastTLS 初始化失败，使用 curl 作为备用
            emit debugLog("[OrderAPI] 🔍 UltraFastTLS初始化失败，回退到curl");
            m_networkEngine = NetworkEngine::CURL;
            emit debugLog("[OrderAPI] 🔄 网络引擎已设置为CURL");
        }
    } catch (const std::exception& e) {
        emit debugLog(QString("[UltraFastTLS] 初始化异常: %1，回退到curl").arg(e.what()));
        m_ultraFastTLS = nullptr;
        m_debugMonitor = nullptr;
        m_networkEngine = NetworkEngine::CURL;
    } catch (...) {
        emit debugLog("[UltraFastTLS] 初始化未知异常，回退到curl");
        m_ultraFastTLS = nullptr;
        m_debugMonitor = nullptr;
        m_networkEngine = NetworkEngine::CURL;
    }

    // 默认直连-QNAM (长连接)
    m_networkManager = managerForProxy("", 0, "http");

    // 每个实例都需要能收到 finished 信号，使用唯一连接标志防止重复
    connect(m_networkManager, &QNetworkAccessManager::finished, this, &OrderAPI::onNetworkReplyFinished, Qt::UniqueConnection);

    // ------------- 预热：HEAD (一次即可) -------------
    static bool s_preheatedDirect = false;
    if (!s_preheatedDirect) {
        QNetworkRequest warm(QUrl("https://server.dailiantong.com.cn/"));
        warm.setAttribute(QNetworkRequest::Http2AllowedAttribute, true); // 允许 H2
        m_networkManager->head(warm);
        // 预热仅执行一次
        s_preheatedDirect = true;
        markActivity();
    }

    // -------- Keep-Alive --------
    m_keepAliveTimer = new QTimer(this);
    m_keepAliveTimer->setInterval(25000); // 25 s
    connect(m_keepAliveTimer, &QTimer::timeout, this, &OrderAPI::sendKeepAlive);
    m_keepAliveTimer->start();
    
    // -------- 创建抢单专用网络管理器--------
    m_acceptManager = new QNetworkAccessManager(this);
    
    // -------- 抢单通道预热定时器--------
    m_acceptPreheater = new QTimer(this);
    m_acceptPreheater->setInterval(25000); // 25秒
    connect(m_acceptPreheater, &QTimer::timeout, this, &OrderAPI::preheatAcceptChannel);
    m_acceptPreheater->start();
    
    // 立即预热一次
    preheatAcceptChannel();
    
    // 连接直接抢单信号到处理槽（使用直接连接，在同一线程处理）
    connect(this, &OrderAPI::takeOrder, this, &OrderAPI::processAcceptOrder, Qt::DirectConnection);
    emit debugLog("[初始化] 已连接快速路径抢单信号，使用Qt::DirectConnection确保在同一线程处理");

    // 默认每次拉取20条订单，可通过 setPageSize 调整
    m_pageSize = 20;
}

OrderAPI::~OrderAPI()
{
    if (m_currentReply) {
        m_currentReply->abort();
        m_currentReply->deleteLater();
    }
}

void OrderAPI::sendKeepAlive()
{
    qint64 now = QDateTime::currentMSecsSinceEpoch();
    if (now - s_lastDirectActivity < 20000) {
        return; // 20s 内已有网络活动，跳过保活
    }
    QNetworkRequest req(QUrl("https://server.dailiantong.com.cn/"));
    req.setAttribute(QNetworkRequest::Http2AllowedAttribute, true);
    m_networkManager->head(req);
    markActivity();    // 更新活动时间
}

void OrderAPI::markActivity()
{
    s_lastDirectActivity = QDateTime::currentMSecsSinceEpoch();
}

// 其他基本方法的简化版本...

void OrderAPI::findCurlPath()
{
    // 简化的curl路径查找
    m_curlPath = "curl.exe";
}

void OrderAPI::preheatAcceptChannel()
{
    // 简化版本
}

bool OrderAPI::isOrderMatchingCriteria(const QJsonObject &orderObj)
{
    // 简化版本
    return false;
}

void OrderAPI::processAcceptOrder(const QString &serialNo, const QString &stamp,
                                 const QString &userId, const QString &payPass,
                                 const QString &loginId, const QString &proxyHost, 
                                 int proxyPort, const QString &proxyUser, const QString &proxyPass)
{
    // 简化版本
    Q_UNUSED(serialNo);
    Q_UNUSED(stamp);
    Q_UNUSED(userId);
    Q_UNUSED(payPass);
    Q_UNUSED(loginId);
    Q_UNUSED(proxyHost);
    Q_UNUSED(proxyPort);
    Q_UNUSED(proxyUser);
    Q_UNUSED(proxyPass);
}

void OrderAPI::onNetworkReplyFinished()
{
    // 简化版本
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }
    
    QString url = reply->url().toString();
    emit debugLog("收到网络响应: " + url);
    
    if (reply->error() != QNetworkReply::NoError) {
        emit networkError(reply->errorString());
        reply->deleteLater();
        return;
    }
    
    QByteArray data = reply->readAll();
    
    if (url.contains("GoHome")) {
        parseLoginResponse(data);
    } else if (url.contains("LevelOrderList")) {
        parseOrderRefreshResponse(data);
    } else if (url.contains("NewLevelOrderAccept")) {
        parseOrderAcceptResponse(data);
    } else if (url.contains("UserInfoList")) {
        parseUserInfoResponse(data);
    }
    
    reply->deleteLater();
    markActivity();
}

void OrderAPI::parseLoginResponse(const QByteArray &data)
{
    // 简化版本
    Q_UNUSED(data);
}

void OrderAPI::parseOrderRefreshResponse(const QByteArray &data)
{
    // 简化版本
    static int s_totalRefresh = 0;
    static int s_successRefresh = 0;
    ++s_totalRefresh;

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    if (error.error != QJsonParseError::NoError) {
        emit orderRefreshResult(false, "JSON解析错误: " + error.errorString(), QJsonArray(), 0);
        return;
    }

    QJsonObject obj = doc.object();
    QJsonArray orderList;
    int recordCount = 0;
    
    // 简化的解析逻辑
    if (obj.contains("Data") && obj["Data"].isObject()) {
        QJsonObject dataObj = obj["Data"].toObject();
        if (dataObj.contains("LevelOrderList") && dataObj["LevelOrderList"].isArray()) {
            orderList = dataObj["LevelOrderList"].toArray();
            recordCount = dataObj["RecordCount"].toInt();
        }
    }
    
    ++s_successRefresh;
    emit orderRefreshResult(true, "获取成功", orderList, recordCount);
}

void OrderAPI::parseOrderAcceptResponse(const QByteArray &data)
{
    // 简化版本
    Q_UNUSED(data);
}

void OrderAPI::parseUserInfoResponse(const QByteArray &data)
{
    // 简化版本
    Q_UNUSED(data);
} 