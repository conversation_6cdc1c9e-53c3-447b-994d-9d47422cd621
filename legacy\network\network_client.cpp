#include "network_client.h"
#include "../utils/simple_logger.h"
#include "../api/api_constants.h"
#include <QProcess>
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <chrono>

// ==================== 构造函数 ====================

NetworkClient::NetworkClient(QObject* parent)
    : QObject(parent)
{
    findCurlExecutable();
}


// ==================== 核心功能 ====================

NetworkClient::RequestResult NetworkClient::executeRequest(
    const QString& url,
    const QString& postData,
    const QHash<QString, QString>& headers)
{
    // 记录请求开始
    LOG_DEBUG(LogCategory::NETWORK, "执行网络请求: " + Utils::truncate(url, 50));
    emit requestStarted(url);

    // 执行请求并计时
    Utils::Timer timer;
    auto result = executeCurlRequest(url, postData, headers);
    result.elapsedMs = timer.elapsed();

    // 记录请求完成
    LOG_INFO(LogCategory::NETWORK, QString("请求完成: %1, %2ms")
             .arg(result.success ? "成功" : "失败")
             .arg(result.elapsedMs));

    emit requestFinished(url, result.success, result.elapsedMs);
    return result;
}


// ==================== 状态查询 ====================

bool NetworkClient::isAvailable() const
{
    QFileInfo curlFile(m_curlPath);
    return curlFile.exists() && curlFile.isExecutable();
}

// ==================== 内部实现 ====================

NetworkClient::RequestResult NetworkClient::executeCurlRequest(
    const QString& url,
    const QString& postData,
    const QHash<QString, QString>& headers)
{
    // 检查curl可用性
    if (!isAvailable()) {
        return {false, "", "curl不可用，路径: " + m_curlPath};
    }

    // 启动curl进程
    QProcess process;
    process.start(m_curlPath, buildCurlArguments(url, postData, headers));

    // 等待进程完成
    if (!process.waitForFinished(m_timeoutMs + 10000)) {
        process.kill();
        return {false, "", "curl请求超时"};
    }

    // 处理结果
    return process.exitCode() == 0
        ? RequestResult{true, decodeResponse(process.readAllStandardOutput()), "", 200}
        : RequestResult{false, "", QString("curl失败，退出码: %1").arg(process.exitCode())};
}

QStringList NetworkClient::buildCurlArguments(const QString& url, const QString& postData, 
                                             const QHash<QString, QString>& headers)
{
    QStringList arguments;
    
    // 基本参数
    arguments << "--silent" << "--show-error" << "--location";
    arguments << "--max-time" << QString::number(m_timeoutMs / 1000);
    arguments << "--connect-timeout" << "30";
    arguments << "--retry" << QString::number(m_retryCount);
    arguments << "--retry-delay" << "1";
    
    // 默认请求头
    arguments << "--header" << "Content-Type: application/x-www-form-urlencoded; charset=UTF-8";
    arguments << "--header" << "Accept: application/json, text/plain, */*";
    arguments << "--header" << "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8";
    arguments << "--header" << "Accept-Encoding: gzip, deflate, br";
    arguments << "--header" << "Connection: keep-alive";
    arguments << "--header" << "Cache-Control: no-cache";
    arguments << "--header" << "User-Agent: " + ApiConstants::UserAgents::QUARK_BROWSER;
    
    // 自定义请求头
    for (auto it = headers.constBegin(); it != headers.constEnd(); ++it) {
        arguments << "--header" << QString("%1: %2").arg(it.key(), it.value());
    }
    
    // POST数据
    if (!postData.isEmpty()) {
        arguments << "--data" << postData;
    }
    
    // URL
    arguments << url;
    
    return arguments;
}

void NetworkClient::findCurlExecutable()
{
    // 使用工具函数简化路径检查
    auto tryPath = [this](const QString& path) {
        if (Utils::isExecutable(path)) {
            m_curlPath = path;
            LOG_INFO(LogCategory::SYSTEM, "找到curl: " + path);
            return true;
        }
        return false;
    };

    const QStringList paths = {
        "C:/Users/<USER>/Documents/curl-8.15.0_4-win64-mingw/curl-8.15.0_4-win64-mingw/bin/curl.exe",
        "C:/Windows/System32/curl.exe", "C:/curl/bin/curl.exe", "curl.exe"
    };

    // 使用工具函数简化查找
    if (Utils::any_of(paths, tryPath)) return;

    // PATH查找
    QProcess process;
    process.start("where", {"curl"});
    if (process.waitForFinished(3000)) {
        auto lines = QString::fromLocal8Bit(process.readAllStandardOutput()).split('\n');
        if (!lines.isEmpty() && tryPath(lines.first().trimmed())) return;
    }

    LOG_WARNING(LogCategory::SYSTEM, "未找到curl，网络功能将不可用");
}

QString NetworkClient::decodeResponse(const QByteArray& responseData)
{
    // 尝试UTF-8解码
    QString response = QString::fromUtf8(responseData);
    
    // 如果UTF-8解码失败（包含乱码字符），尝试本地编码
    if (response.contains("�")) {
        response = QString::fromLocal8Bit(responseData);
        LOG_DEBUG(LogCategory::NETWORK, "检测到乱码，使用本地编码重新解析");
    }
    
    return response;
}
