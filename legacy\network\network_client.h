#ifndef NETWORK_CLIENT_H
#define NETWORK_CLIENT_H

#include <QObject>
#include <QString>
#include <QHash>
#include "../utils/utils.h"

/**
 * @brief 网络客户端 - 专门负责网络请求
 *
 * 单一责任：只负责执行网络请求，不处理业务逻辑
 */
class NetworkClient : public QObject
{
    Q_OBJECT

public:
    // ==================== 数据结构 ====================

    /**
     * @brief 网络请求结果
     */
    struct RequestResult {
        bool    success         = false;
        QString data;
        QString errorMessage;
        int     httpStatusCode  = 0;
        qint64  elapsedMs       = 0;

        bool isSuccess() const { return success && !data.isEmpty(); }
    };

    // ==================== 构造函数 ====================

    explicit NetworkClient(QObject* parent = nullptr);

    // ==================== 核心功能 ====================

    RequestResult executeRequest(const QString& url,
                                const QString& postData = "",
                                const QHash<QString, QString>& headers = {});

    // ==================== 配置方法 ====================

    void setCurlPath(const QString& path)   { m_curlPath = path; }
    void setTimeout(int timeoutMs)          { m_timeoutMs = timeoutMs; }
    void setRetryCount(int count)           { m_retryCount = count; }

    // ==================== 状态查询 ====================

    bool    isAvailable() const;
    QString getCurlPath() const             { return m_curlPath; }

signals:
    // ==================== 信号 ====================

    void requestStarted(const QString& url);
    void requestFinished(const QString& url, bool success, qint64 elapsedMs);

private:
    // ==================== 成员变量 ====================

    QString m_curlPath;
    int     m_timeoutMs     = 30000;
    int     m_retryCount    = 2;

    // ==================== 内部方法 ====================

    RequestResult executeCurlRequest(const QString& url,
                                   const QString& postData,
                                   const QHash<QString, QString>& headers);

    QStringList buildCurlArguments(const QString& url,
                                  const QString& postData,
                                  const QHash<QString, QString>& headers);

    void    findCurlExecutable();
    QString decodeResponse(const QByteArray& responseData);
};

#endif // NETWORK_CLIENT_H
