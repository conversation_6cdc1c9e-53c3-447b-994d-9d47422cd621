#include "ultrafasttls.h"
#include "ultrafasttls_debug_monitor.h"
#include <QDebug>
#include <QUrl>
#include <QRegularExpression>
#include <QMutexLocker>
// 新架构集成
#include <app_config.h>
#include <logger.h>

UltraFastTLS::UltraFastTLS(QObject *parent)
    : QObject(parent)
{
    // 新架构集成：使用统一日志
    NEW_LOG_INFO(NewLogCategory::NETWORK, "UltraFastTLS 引擎初始化");
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("网络超时: %1ms").arg(NETWORK_CONFIG.timeout));

    // 初始化定时器 - 使用配置系统
    m_keepAliveTimer = new QTimer(this);
    m_keepAliveTimer->setInterval(NETWORK_CONFIG.timeout); // 使用配置的超时时间
    connect(m_keepAliveTimer, &QTimer::timeout, this, &UltraFastTLS::onKeepAliveTimer);

    m_cleanupTimer = new QTimer(this);
    m_cleanupTimer->setInterval(60000); // 60秒清理一次
    connect(m_cleanupTimer, &QTimer::timeout, this, &UltraFastTLS::onConnectionCleanup);
}

UltraFastTLS::~UltraFastTLS()
{
    // 清理所有连接
    QMutexLocker locker(&m_poolMutex);
    m_connectionPool.clear();

    // 清理SSL上下文
#ifdef OPENSSL_FOUND
    if (m_sslContext) {
        SSL_CTX_free(m_sslContext);
        m_sslContext = nullptr;
    }
#else
    if (m_schannelInitialized) {
        // 清理SChannel资源
        m_schannelInitialized = false;
    }
#endif

    // 清理WSA
    if (m_wsaInitialized) {
        WSACleanup();
        m_wsaInitialized = false;
    }
}

bool UltraFastTLS::initialize()
{
    // 简化初始化，避免卡死
    if (m_initialized) {
        return true;
    }

    // ========== 运行时性能优化配置 ==========

    // 设置进程优先级为高优先级（仅Windows）
#ifdef _WIN32
    SetPriorityClass(GetCurrentProcess(), HIGH_PRIORITY_CLASS);
    SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_ABOVE_NORMAL);
    emit debugLog("[UltraFastTLS] ✅ 进程优先级已提升");
#endif

    // 初始化Windows Socket API（高性能配置）
#ifdef _WIN32
    WSADATA wsaData;
    const int wsaResult = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (wsaResult != 0) {
        emit debugLog(QString("[UltraFastTLS] ❌ WSAStartup失败，错误码: %1").arg(wsaResult));
        return false;
    }
    m_wsaInitialized = true;
#endif
    
    // 初始化SSL库
    if (!initializeSSL()) {
        emit debugLog("[UltraFastTLS] SSL初始化失败");
        return false;
    }
    
    // 创建连接池
    if (!createConnectionPool()) {
        emit debugLog("[UltraFastTLS] 连接池创建失败");
        return false;
    }
    
    // 启动定时器
    m_keepAliveTimer->start();
    m_cleanupTimer->start();

    m_initialized = true;
    emit debugLog("[UltraFastTLS] 超高性能TLS伪装初始化完成！");
    
    return true;
}

bool UltraFastTLS::initializeSSL()
{
    emit debugLog("[UltraFastTLS] 🔍 initializeSSL() 方法被调用");

    // 开始SSL初始化

#ifdef OPENSSL_FOUND
    emit debugLog("[UltraFastTLS] 🔧 检测到OpenSSL，使用OpenSSL模式");
    // ============================================================
    // 🚀 OpenSSL 高性能初始化
    // ============================================================

#ifdef OPENSSL_3_PLUS
    // 检查OpenSSL版本并记录日志
    const char* version = OpenSSL_version(OPENSSL_VERSION);
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("OpenSSL版本: %1").arg(version));

    // OpenSSL 3.x+ 初始化 (更高性能)
    OPENSSL_init_ssl(OPENSSL_INIT_LOAD_SSL_STRINGS | OPENSSL_INIT_LOAD_CRYPTO_STRINGS, NULL);
    // OpenSSL 3.x+ 模式
#else
    // 检查OpenSSL版本并记录日志
    const char* version = OpenSSL_version(OPENSSL_VERSION);
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("OpenSSL版本: %1").arg(version));

    // OpenSSL 1.1.x 兼容初始化
    SSL_load_error_strings();
    SSL_library_init();
    OpenSSL_add_all_algorithms();
    // OpenSSL 1.1.x 兼容模式
#endif

    // 创建SSL上下文 (支持TLS 1.2/1.3)
    // 创建SSL上下文
    m_sslContext = SSL_CTX_new(TLS_client_method());
    if (!m_sslContext) {
        unsigned long error = ERR_get_error();
        char errorBuffer[256];
        ERR_error_string_n(error, errorBuffer, sizeof(errorBuffer));
        emit debugLog(QString("[SSL初始化] ❌ SSL_CTX_new失败: %1").arg(QString::fromUtf8(errorBuffer)));
        return false;
    }
    // SSL上下文创建成功

    // ============================================================
    // 🔥 SSL上下文性能优化配置
    // ============================================================

    // 1. TLS版本配置 (保守策略，优先兼容性)
    // 很多服务器对TLS 1.3支持不完善，先限制为TLS 1.2
    SSL_CTX_set_min_proto_version(m_sslContext, TLS1_2_VERSION);
    SSL_CTX_set_max_proto_version(m_sslContext, TLS1_2_VERSION);  // 暂时禁用TLS 1.3

    // 2. 会话缓存优化 (大幅提升重连速度)
    SSL_CTX_set_session_cache_mode(m_sslContext, SSL_SESS_CACHE_CLIENT);
    SSL_CTX_sess_set_cache_size(m_sslContext, 1024);  // 缓存1024个会话
    SSL_CTX_set_timeout(m_sslContext, 300);           // 5分钟超时

    // 3. 禁用不必要的功能以提升性能
    SSL_CTX_set_verify(m_sslContext, SSL_VERIFY_NONE, nullptr);  // 跳过证书验证
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_COMPRESSION);    // 禁用压缩
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_SSLv2);         // 禁用SSLv2
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_SSLv3);         // 禁用SSLv3

    // 4. 启用性能优化选项
    SSL_CTX_set_options(m_sslContext, SSL_OP_SINGLE_DH_USE);           // DH密钥重用
    SSL_CTX_set_options(m_sslContext, SSL_OP_SINGLE_ECDH_USE);         // ECDH密钥重用
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_TICKET);               // 禁用会话票据
    SSL_CTX_set_options(m_sslContext, SSL_OP_CIPHER_SERVER_PREFERENCE); // 服务器密码偏好

    // 5. 设置读写缓冲区大小 (优化网络I/O)
    SSL_CTX_set_default_read_buffer_len(m_sslContext, 16384);  // 16KB读缓冲

    // 6. 设置夸克浏览器专用密码套件 (基于JA3指纹)
    const char* cipherList;
    switch (m_browserType) {
        case BrowserFingerprint::QUARK_BROWSER:
            // 完整的夸克浏览器密码套件 (精确匹配JA3)
            cipherList = "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:"
                        "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:"
                        "ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:"
                        "ECDHE-PSK-CHACHA20-POLY1305:ECDHE-PSK-AES256-GCM-SHA384:"
                        "ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:"
                        "AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA:AES256-SHA";
            break;
        case BrowserFingerprint::QUARK_FALLBACK:
            // 简化的夸克浏览器密码套件 (兼容性优先)
            cipherList = "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:"
                        "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:"
                        "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384";
            break;
        default:
            cipherList = "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:"
                        "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256";
    }

    if (SSL_CTX_set_cipher_list(m_sslContext, cipherList) != 1) {
        emit debugLog("[SSL初始化] ❌ 设置密码套件失败");
        return false;
    }

    // 7. 设置TLS 1.3密码套件
    if (!SSL_CTX_set_ciphersuites(m_sslContext, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256")) {
        emit debugLog("[SSL初始化] ⚠️ TLS 1.3密码套件设置失败 (可能不支持)");
    }

    emit debugLog("[SSL初始化] ✅ 超高性能OpenSSL配置完成");
    emit debugLog(QString("[SSL初始化] 🎯 浏览器类型: %1").arg(static_cast<int>(m_browserType)));

#else
    emit debugLog("[UltraFastTLS] 🔧 未检测到OpenSSL，使用Windows SChannel模式");
    // ============================================================
    // 🔥 Windows SChannel 初始化 (原生TLS)
    // ============================================================

    emit debugLog("[SSL初始化] 🔧 使用Windows原生TLS (SChannel)");

    // 初始化SChannel凭据
    ZeroMemory(&m_schannelCred, sizeof(m_schannelCred));
    m_schannelCred.dwVersion = SCHANNEL_CRED_VERSION;
    m_schannelCred.grbitEnabledProtocols = SP_PROT_TLS1_2 | SP_PROT_TLS1_3;
    m_schannelCred.dwFlags = SCH_CRED_NO_DEFAULT_CREDS | SCH_CRED_MANUAL_CRED_VALIDATION;

    m_schannelInitialized = true;

    emit debugLog("[SSL初始化] ✅ Windows SChannel配置完成");
    emit debugLog("[SSL初始化] ⚠️ 注意: SChannel模式下指纹匹配度有限");

#endif

    return true;
}

bool UltraFastTLS::createConnectionPool()
{
    emit debugLog(QString("[UltraFastTLS] 创建连接池，大小: %1").arg(m_poolSize));

    QMutexLocker locker(&m_poolMutex);

    // 预建立到主服务器的连接
    for (int i = 0; i < m_poolSize; ++i) {
        auto conn = createTLSConnection("server.dailiantong.com.cn", 443);
        if (conn) {
            m_connectionPool.push_back(std::move(conn));
            emit debugLog(QString("[UltraFastTLS] 连接 %1/%2 创建成功").arg(i + 1).arg(m_poolSize));
        } else {
            emit debugLog(QString("[UltraFastTLS] 连接 %1/%2 创建失败").arg(i + 1).arg(m_poolSize));
        }
    }

    emit debugLog(QString("[UltraFastTLS] 连接池创建完成，成功: %1/%2")
                  .arg(m_connectionPool.size()).arg(m_poolSize));
    
    return !m_connectionPool.empty();
}

std::unique_ptr<UltraFastTLS::ConnectionInfo> UltraFastTLS::createTLSConnection(const QString &host, int port)
{
    emit debugLog(QString("[UltraFastTLS] 🔄 开始创建TLS连接: %1:%2").arg(host).arg(port));

    auto conn = std::make_unique<ConnectionInfo>();
    conn->serverHost = host;
    conn->serverPort = port;

    // 创建Socket
    emit debugLog("[UltraFastTLS] 📡 创建socket...");
    conn->socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (conn->socket == INVALID_SOCKET) {
        emit debugLog(QString("[UltraFastTLS] ❌ 创建socket失败: %1").arg(WSAGetLastError()));
        return nullptr;
    }
    emit debugLog("[UltraFastTLS] ✅ Socket创建成功");

    // 设置socket选项
    int optval = 1;
    setsockopt(conn->socket, IPPROTO_TCP, TCP_NODELAY, (char*)&optval, sizeof(optval));

    // 解析主机地址
    struct addrinfo hints;
    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_INET;
    hints.ai_socktype = SOCK_STREAM;

    struct addrinfo* result = nullptr;
    int ret = getaddrinfo(host.toUtf8().constData(), QString::number(port).toUtf8().constData(), &hints, &result);
    if (ret != 0) {
        emit debugLog(QString("[UltraFastTLS] ❌ 解析主机失败: %1, 错误码: %2").arg(host).arg(ret));
        return nullptr;
    }
    // 连接到服务器 (使用 :: 明确指定全局 connect 函数)
    emit debugLog(QString("[UltraFastTLS] 🌐 正在连接服务器: %1:%2").arg(host).arg(port));
    ret = ::connect(conn->socket, result->ai_addr, (int)result->ai_addrlen);
    freeaddrinfo(result);

    if (ret == SOCKET_ERROR) {
        int error = WSAGetLastError();
        QString errorDetail;
        switch (error) {
            case WSAECONNREFUSED:
                errorDetail = "连接被拒绝";
                break;
            case WSAENETUNREACH:
                errorDetail = "网络不可达";
                break;
            case WSAEHOSTUNREACH:
                errorDetail = "主机不可达";
                break;
            case WSAETIMEDOUT:
                errorDetail = "连接超时";
                break;
            case WSAEACCES:
                errorDetail = "权限被拒绝";
                break;
            default:
                errorDetail = "未知错误";
                break;
        }
        emit debugLog(QString("[UltraFastTLS] ❌ 连接服务器失败: %1:%2").arg(host).arg(port));
        emit debugLog(QString("  错误码: %1 (%2)").arg(error).arg(errorDetail));
        closesocket(conn->socket);
        return nullptr;
    }
    emit debugLog("[UltraFastTLS] ✅ TCP连接建立成功");

#ifdef OPENSSL_FOUND
    // 创建SSL连接
    emit debugLog("[UltraFastTLS] 🔐 开始创建SSL连接...");
    conn->ssl = SSL_new(m_sslContext);
    if (!conn->ssl) {
        emit debugLog("[UltraFastTLS] ❌ SSL_new失败");
        closesocket(conn->socket);
        return nullptr;
    }
    emit debugLog("[UltraFastTLS] ✅ SSL对象创建成功");

    // 设置完美的TLS指纹
    emit debugLog("[UltraFastTLS] 🎭 设置TLS指纹...");
    if (!setupPerfectTLSFingerprint(conn->ssl)) {
        emit debugLog("[UltraFastTLS] ❌ 设置TLS指纹失败");
        SSL_free(conn->ssl);
        closesocket(conn->socket);
        return nullptr;
    }
    emit debugLog("[UltraFastTLS] ✅ TLS指纹设置成功");

    // 绑定socket到SSL
    emit debugLog("[UltraFastTLS] 🔗 绑定socket到SSL...");
    SSL_set_fd(conn->ssl, (int)conn->socket);
    emit debugLog("[UltraFastTLS] ✅ Socket绑定到SSL完成");

    // 执行TLS握手 - 添加超时机制避免卡死
    emit debugLog("[UltraFastTLS] 🤝 开始TLS握手...");

    // 设置socket为非阻塞模式
    u_long mode = 1;
    ioctlsocket(conn->socket, FIONBIO, &mode);
    emit debugLog("[UltraFastTLS] 🔄 设置为非阻塞模式");

    // 开始TLS握手
    auto handshakeStart = std::chrono::steady_clock::now();
    const int handshakeTimeoutMs = 10000; // 10秒超时
    int handshakeAttempts = 0;

    while (true) {
        handshakeAttempts++;
        emit debugLog(QString("[UltraFastTLS] 🤝 TLS握手尝试 #%1").arg(handshakeAttempts));

        ret = SSL_connect(conn->ssl);
        if (ret == 1) {
            emit debugLog("[UltraFastTLS] ✅ TLS握手成功！");
            break; // 握手成功
        }

        int sslError = SSL_get_error(conn->ssl, ret);

        // 在安静模式下，只记录真正的错误，不记录正常的握手过程
        if (!m_quietMode || (sslError != SSL_ERROR_WANT_READ && sslError != SSL_ERROR_WANT_WRITE)) {
            emit debugLog(QString("[UltraFastTLS] 🔍 SSL_connect返回: %1, SSL错误: %2").arg(ret).arg(sslError));
        }

        if (sslError == SSL_ERROR_WANT_READ || sslError == SSL_ERROR_WANT_WRITE) {
            // 检查超时
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - handshakeStart).count();
            if (elapsed > handshakeTimeoutMs) {
                emit debugLog(QString("[UltraFastTLS] ❌ TLS握手超时 (耗时: %1ms)").arg(elapsed));
                SSL_free(conn->ssl);
                closesocket(conn->socket);
                return nullptr;
            }

            emit debugLog(QString("[UltraFastTLS] ⏳ 等待握手数据 (已耗时: %1ms)").arg(elapsed));
            // 非阻塞等待
            QEventLoop loop;
            QTimer::singleShot(50, &loop, &QEventLoop::quit);
            loop.exec();
            continue;
        } else {
            // 其他错误，直接失败
            emit debugLog(QString("[UltraFastTLS] ❌ TLS握手遇到错误: %1").arg(sslError));
            break;
        }
    }

    // 恢复阻塞模式
    mode = 0;
    ioctlsocket(conn->socket, FIONBIO, &mode);
    emit debugLog("[UltraFastTLS] 🔄 恢复阻塞模式");

    if (ret != 1) {
        int sslError = SSL_get_error(conn->ssl, ret);

        // 详细的SSL错误分析
        QString errorDetail;
        switch (sslError) {
            case SSL_ERROR_NONE:
                errorDetail = "无错误";
                break;
            case SSL_ERROR_SSL:
                errorDetail = "SSL协议错误";
                break;
            case SSL_ERROR_WANT_READ:
                errorDetail = "需要更多数据读取";
                break;
            case SSL_ERROR_WANT_WRITE:
                errorDetail = "需要更多数据写入";
                break;
            case SSL_ERROR_WANT_X509_LOOKUP:
                errorDetail = "X509证书查找错误";
                break;
            case SSL_ERROR_SYSCALL:
                errorDetail = "系统调用错误";
                break;
            case SSL_ERROR_ZERO_RETURN:
                errorDetail = "连接被对方关闭";
                break;
            case SSL_ERROR_WANT_CONNECT:
                errorDetail = "连接未完成";
                break;
            case SSL_ERROR_WANT_ACCEPT:
                errorDetail = "接受未完成";
                break;
            default:
                errorDetail = "未知错误";
                break;
        }

        // 获取OpenSSL错误队列中的详细错误
        unsigned long opensslError = ERR_get_error();
        char errorBuffer[256];
        ERR_error_string_n(opensslError, errorBuffer, sizeof(errorBuffer));

        emit debugLog(QString("[UltraFastTLS] ❌ TLS握手失败详情:"));
        emit debugLog(QString("  SSL错误码: %1 (%2)").arg(sslError).arg(errorDetail));
        emit debugLog(QString("  返回值: %1").arg(ret));
        emit debugLog(QString("  OpenSSL错误: %1").arg(QString::fromUtf8(errorBuffer)));
        emit debugLog(QString("  错误号: 0x%1").arg(opensslError, 0, 16));

        return nullptr;
    }
    // TLS握手成功
#else
    // Windows SChannel TLS握手
    if (!setupPerfectTLSFingerprint(conn.get())) {
        emit debugLog("[UltraFastTLS] 设置SChannel TLS失败");
        return nullptr;
    }

    emit debugLog("[UltraFastTLS] ⚠️ SChannel模式: 基础TLS功能");
#endif
    
    emit debugLog(QString("[UltraFastTLS] TLS连接建立成功: %1:%2").arg(host).arg(port));
    return conn;
}

bool UltraFastTLS::setupPerfectTLSFingerprint(SSL* ssl)
{
    // 设置SNI (动态设置，避免固定指纹)
    SSL_set_tlsext_host_name(ssl, "server.dailiantong.com.cn");

    // 根据浏览器指纹类型设置TLS指纹
    switch (m_browserType) {
        case BrowserFingerprint::QUARK_BROWSER:
            return setupQuarkBrowserFingerprint(ssl);
        case BrowserFingerprint::QUARK_FALLBACK:
            return setupQuarkFallbackFingerprint(ssl);
        case BrowserFingerprint::WECHAT_BROWSER:
            return setupWechatBrowserFingerprint(ssl);
        case BrowserFingerprint::QUARK_ANDROID14:
            return setupQuarkAndroid14Fingerprint(ssl);
        default:
            return setupQuarkBrowserFingerprint(ssl);
    }
}

bool UltraFastTLS::setupQuarkBrowserFingerprint(SSL* ssl)
{
    // ============================================================
    // 夸克浏览器 7.14.5.880 完美TLS指纹伪装
    // 基于您提供的真实指纹数据
    // ============================================================

    // 设置夸克浏览器指纹

    // 1. TLS版本设置 (基于JA3: 771)
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 2. 密码套件设置 (基于JA3: 4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53)
    // 精确匹配夸克浏览器的密码套件顺序
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"           // 4865
        "TLS_AES_256_GCM_SHA384:"           // 4866
        "TLS_CHACHA20_POLY1305_SHA256:"     // 4867
        "ECDHE-ECDSA-AES128-GCM-SHA256:"    // 49195
        "ECDHE-ECDSA-AES256-GCM-SHA384:"    // 49199
        "ECDHE-RSA-AES128-GCM-SHA256:"      // 49196
        "ECDHE-RSA-AES256-GCM-SHA384:"      // 49200
        "ECDHE-PSK-CHACHA20-POLY1305:"      // 52393
        "ECDHE-PSK-AES256-GCM-SHA384:"      // 52392
        "ECDHE-RSA-AES128-SHA256:"          // 49171
        "ECDHE-RSA-AES256-SHA384:"          // 49172
        "AES128-GCM-SHA256:"                // 156
        "AES256-GCM-SHA384:"                // 157
        "AES128-SHA:"                       // 47
        "AES256-SHA";                       // 53

    if (!SSL_set_cipher_list(ssl, cipher_list)) {
        emit debugLog("[夸克指纹] ❌ 密码套件设置失败");
        return false;
    }

    // 3. TLS 1.3密码套件 (精确匹配)
    if (!SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256")) {
        emit debugLog("[夸克指纹] ⚠️ TLS 1.3密码套件设置失败，使用默认套件");
        // 不返回 false，继续使用默认 TLS 1.3 密码套件
    } else {
        // TLS 1.3密码套件设置成功
    }

    // 4. 椭圆曲线设置 (基于JA3: 29-23-24)
    const int curves[] = {
        NID_X9_62_prime256v1,  // 23 (P-256)
        NID_secp384r1,         // 24 (P-384)
        NID_X25519             // 29 (X25519)
    };

    if (!SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]))) {
        emit debugLog("[夸克指纹] ⚠️ 椭圆曲线设置失败，使用默认曲线");
        // 不返回 false，继续使用默认椭圆曲线
    } else {
        // 椭圆曲线设置成功
    }

    // 5. 签名算法设置 (基于JA4_R签名算法，兼容OpenSSL 3.x)
    // 使用字符串方式设置签名算法，更兼容
    const char* sig_algs_str = "ECDSA+SHA256:ECDSA+SHA384:RSA+SHA256:RSA+SHA384:RSA+SHA512";

    if (!SSL_set1_sigalgs_list(ssl, sig_algs_str)) {
        emit debugLog("[夸克指纹] ⚠️ 高级签名算法设置失败，使用默认算法");
        // 不返回 false，继续使用默认签名算法
    } else {
        // 签名算法设置成功
    }

    // 6. ALPN协议设置 (HTTP/2优先，匹配夸克浏览器)
    const unsigned char alpn_protos[] = {
        2, 'h', '2',        // HTTP/2
        8, 'h', 't', 't', 'p', '/', '1', '.', '1'  // HTTP/1.1
    };

    if (SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos)) != 0) {
        emit debugLog("[夸克指纹] ❌ ALPN设置失败");
        return false;
    }

    // 7. 扩展设置 (基于JA3扩展: 65281-27-5-43-10-17513-11-13-45-18-23-0-16-51-35-21)
    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);  // OCSP Stapling
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);           // 跳过证书验证以提高速度

    // 8. 设置特殊的夸克浏览器User-Agent相关配置
    // 模拟Android 15 + Chrome 123.0.6312.80 + Quark 7.14.5.880

    emit debugLog("[夸克指纹] ✅ 夸克浏览器 7.14.5.880 指纹设置完成");
    emit debugLog("[夸克指纹] 📱 模拟设备: Android 15 (V2307A)");
    emit debugLog("[夸克指纹] 🌐 内核版本: Chrome 123.0.6312.80");
    emit debugLog("[夸克指纹] 🔒 JA3哈希: 7831fa6465c3511f38bd96c933a8459e");

    return true;
}

QString UltraFastTLS::executeRequest(const QString &url, const QString &postData)
{
    const auto requestStartTime = std::chrono::high_resolution_clock::now();

    // 新架构日志：更详细的请求信息
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("UltraFastTLS 开始处理请求: %1").arg(url));
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("请求数据大小: %1 字节").arg(postData.size()));

    // 明确标识UltraFastTLS引擎正在工作
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, "⚡ UltraFastTLS引擎开始处理请求");
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("🔍 Keep-Alive检测统计: %1次虚假keep-alive").arg(m_stats.fakeKeepAliveCount));

    // 如果有调试监控器，创建操作监控
    QString operationId;
    if (m_debugMonitor) {
        operationId = m_debugMonitor->startOperation(url, 30000); // 30秒超时
        m_debugMonitor->updateOperationState(operationId, UltraFastTLSDebugMonitor::OperationState::INITIALIZING, "解析URL");
    }

    // 第一步：解析URL
    const ParsedUrlInfo parsedUrl = parseUrlString(url);
    if (parsedUrl.hostName.isEmpty()) {
        emit debugLog("[UltraFastTLS] ❌ URL解析失败");
        if (m_debugMonitor && !operationId.isEmpty()) {
            m_debugMonitor->addOperationMessage(operationId, "URL解析失败");
            m_debugMonitor->completeOperation(operationId, false, "URL解析失败");
        }
        updateRequestPerformanceStats(requestStartTime, false, 0);
        return QString();
    }

    if (m_debugMonitor && !operationId.isEmpty()) {
        m_debugMonitor->addOperationMessage(operationId, QString("URL解析成功: %1:%2").arg(parsedUrl.hostName).arg(parsedUrl.portNumber));
        m_debugMonitor->updateOperationState(operationId, UltraFastTLSDebugMonitor::OperationState::CREATING_CONNECTION, "获取连接");
    }

    // 第二步：获取连接并执行请求
    ConnectionInfo* connection = borrowConnection(parsedUrl.hostName, parsedUrl.portNumber);
    if (!connection) {
        emit debugLog("[UltraFastTLS] ❌ 无法获取可用连接");
        if (m_debugMonitor && !operationId.isEmpty()) {
            m_debugMonitor->addOperationMessage(operationId, "无法获取可用连接");
            m_debugMonitor->completeOperation(operationId, false, "连接获取失败");
        }
        updateRequestPerformanceStats(requestStartTime, false, 0);
        return QString();
    }

    if (m_debugMonitor && !operationId.isEmpty()) {
        m_debugMonitor->addOperationMessage(operationId, "连接获取成功");
        m_debugMonitor->updateOperationState(operationId, UltraFastTLSDebugMonitor::OperationState::SENDING_REQUEST, "执行HTTP请求");
    }

    // 第三步：执行单次请求
    QString response = executeSingleHttpRequest(connection, parsedUrl, postData);

    // 第四步：如果失败，尝试智能重试
    if (response.isEmpty()) {
        emit debugLog("[UltraFastTLS] 🔄 首次请求失败，开始智能重试...");

        // 标记连接无效并归还
        connection->isValid = false;
        returnConnection(connection);

        // 构建HTTP请求用于重试
        const QString httpMethod = postData.isEmpty() ? "GET" : "POST";
        const QString httpRequest = buildHTTP11Request(
            httpMethod, parsedUrl.requestPath, parsedUrl.hostName, postData);

        // 执行智能重试（最多3次，每次间隔递增）
        response = handleSmartRetry(parsedUrl, httpRequest);
    } else {
        // 成功时归还连接
        returnConnection(connection);
    }

    // 第五步：更新性能统计并返回结果
    const bool requestSuccess = !response.isEmpty();
    updateRequestPerformanceStats(requestStartTime, requestSuccess, response.length());

    emit debugLog(QString("[UltraFastTLS] %1 请求完成，响应长度: %2")
                  .arg(requestSuccess ? "✅" : "❌")
                  .arg(response.length()));

    // 安静模式下只在有真正错误时才输出统计
    if (!m_quietMode && m_stats.totalRequests % 10 == 0) {
        // 只有当有真正的SSL错误时才显示统计
        if (m_stats.sslErrors > 0) {
            emit debugLog(QString("[UltraFastTLS] ⚠️ 错误统计: SSL错误%1次, 复用失败%2次")
                         .arg(m_stats.sslErrors)
                         .arg(m_stats.reuseFailures));
        }
    }

    // 完成调试监控
    if (m_debugMonitor && !operationId.isEmpty()) {
        m_debugMonitor->addOperationMessage(operationId, QString("请求完成，响应长度: %1").arg(response.length()));
        m_debugMonitor->completeOperation(operationId, requestSuccess,
                                        requestSuccess ? QString("成功，响应长度: %1").arg(response.length()) : "请求失败");
    }

    return response;
}



UltraFastTLS::ConnectionInfo* UltraFastTLS::borrowConnection(const QString &host, int port)
{
    emit debugLog(QString("[UltraFastTLS] 📋 请求连接: %1:%2").arg(host).arg(port));

    // 修复死锁：分离查找和创建操作
    {
        NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("🔍 尝试获取连接到 %1:%2").arg(host).arg(port));
        QMutexLocker locker(&m_poolMutex);
        NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("📊 连接池状态 - 总连接数: %1").arg(m_connectionPool.size()));

        // 查找可用连接
        NEW_LOG_DEBUG(NewLogCategory::NETWORK, "🔍 开始查找可复用的连接");
        for (auto& conn : m_connectionPool) {
            NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("🔍 检查连接: inUse=%1, isValid=%2, host=%3:%4")
                         .arg(conn->inUse).arg(conn->isValid).arg(conn->serverHost).arg(conn->serverPort));
            if (!conn->inUse && conn->isValid && conn->serverHost == host && conn->serverPort == port) {
                // 简化连接检查，避免死锁
                auto connectionAge = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::steady_clock::now() - conn->lastUsed).count();

                // 实验：强制一直复用，永不重连！
                // 完全移除时间限制，看看会发生什么
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("🔄 强制复用连接(年龄%1秒) - 永不重连实验").arg(connectionAge));
                // 注释掉时间检查，强制复用
                // if (connectionAge > 15) {
                //     continue;
                // }

                // 实验：强制复用连接，无论服务器是否声明keep-alive
                conn->inUse = true;
                conn->lastUsed = std::chrono::steady_clock::now();
                conn->reuseCount++;  // 增加复用计数
                m_stats.poolHits++;

                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("🔄 强制复用连接(年龄%1秒, 第%2次复用) - 永不重连实验")
                            .arg(connectionAge).arg(conn->reuseCount));
                return conn.get();
            }
        }
        emit debugLog("[UltraFastTLS] 🔓 释放连接池锁");
    } // 释放锁

    // 在锁外创建新连接，避免死锁
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("🆕 创建新连接到 %1:%2 (连接池无可用连接)").arg(host).arg(port));
    auto newConn = createTLSConnection(host, port);
    if (newConn) {
        // 重新获取锁添加到池中
        emit debugLog("[UltraFastTLS] 🔒 重新获取锁添加新连接...");
        QMutexLocker locker(&m_poolMutex);
        newConn->inUse = true;
        ConnectionInfo* ptr = newConn.get();
        m_connectionPool.push_back(std::move(newConn));
        m_stats.poolMisses++;
        emit debugLog("[UltraFastTLS] ✅ 新连接创建成功并添加到池中");
        return ptr;
    }

    emit debugLog("[UltraFastTLS] ❌ 创建新连接失败");
    return nullptr;
}

void UltraFastTLS::returnConnection(ConnectionInfo* conn)
{
    if (!conn) return;

    QMutexLocker locker(&m_poolMutex);
    conn->inUse = false;
    conn->lastUsed = std::chrono::steady_clock::now();
}

QString UltraFastTLS::buildHTTP11Request(const QString &method, const QString &path,
                                        const QString &host, const QString &postData)
{
    QString request;
    request += QString("%1 %2 HTTP/1.1\r\n").arg(method, path);
    request += QString("Host: %1\r\n").arg(host);
    // 智能连接管理：根据请求频率决定是否保持连接
    static int requestCount = 0;
    requestCount++;

    // 每5个请求后主动关闭连接，避免服务器强制关闭
    if (requestCount % 5 == 0) {
        request += "Connection: close\r\n";
    } else {
        request += "Connection: keep-alive\r\n";
        request += "Keep-Alive: timeout=30, max=100\r\n";  // 明确指定keep-alive参数
    }

    if (!postData.isEmpty()) {
        request += QString("Content-Length: %1\r\n").arg(postData.toUtf8().length());
        request += "Content-Type: application/x-www-form-urlencoded\r\n";
    }

    // 使用配置系统的User-Agent
    request += QString("User-Agent: %1\r\n").arg(NETWORK_CONFIG.userAgent);

    // 关键的夸克浏览器标识
    request += "X-Requested-With: com.quark.browser\r\n";

    // 其他重要头部
    request += "Accept: */*\r\n";
    request += "Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7\r\n";
    // 暂时禁用压缩以避免解压缩问题
    request += "Accept-Encoding: identity\r\n";
    request += "Origin: https://m.dailiantong.com\r\n";
    request += "Referer: https://m.dailiantong.com/\r\n";
    request += "Sec-Fetch-Site: cross-site\r\n";
    request += "Sec-Fetch-Mode: cors\r\n";
    request += "Sec-Fetch-Dest: empty\r\n";

    // Client Hints (夸克浏览器WebView环境)
    request += "sec-ch-ua: \"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"\r\n";
    request += "sec-ch-ua-platform: \"Android\"\r\n";
    request += "sec-ch-ua-mobile: ?1\r\n";

    request += "\r\n";

    if (!postData.isEmpty()) {
        request += postData;
    }

    return request;
}

bool UltraFastTLS::sendHTTPRequest(ConnectionInfo* conn, const QString &request)
{
    if (!conn || !conn->ssl) {
        return false;
    }

    QByteArray requestData = request.toUtf8();
    int totalSent = 0;
    int requestSize = requestData.size();

    while (totalSent < requestSize) {
        int sent = SSL_write(conn->ssl, requestData.constData() + totalSent, requestSize - totalSent);
        if (sent <= 0) {
            int sslError = SSL_get_error(conn->ssl, sent);
            emit debugLog(QString("[UltraFastTLS] 发送请求失败: %1").arg(sslError));
            return false;
        }
        totalSent += sent;
    }

    return true;
}

QString UltraFastTLS::readHTTPResponse(ConnectionInfo* conn)
{
    if (!conn || !conn->ssl) {
        return QString();
    }

    QString response;
    char buffer[8192];
    bool headerComplete = false;
    int contentLength = -1;
    int headerLength = 0;
    int retryCount = 0;
    const int maxRetries = 10;

    // 开始读取HTTP响应

    while (true) {
        int received = SSL_read(conn->ssl, buffer, sizeof(buffer) - 1);
        if (received <= 0) {
            int sslError = SSL_get_error(conn->ssl, received);
            emit debugLog(QString("[UltraFastTLS] 🔍 SSL_read返回: %1, SSL错误: %2").arg(received).arg(sslError));

            if (sslError == SSL_ERROR_WANT_READ || sslError == SSL_ERROR_WANT_WRITE) {
                retryCount++;
                if (retryCount > maxRetries) {
                    emit debugLog(QString("[UltraFastTLS] ❌ 读取超时，重试次数: %1").arg(retryCount));
                    break;
                }
                emit debugLog(QString("[UltraFastTLS] 🔄 等待数据，重试: %1/%2").arg(retryCount).arg(maxRetries));
                // 修复：使用非阻塞等待，避免UI卡死
                QEventLoop loop;
                QTimer::singleShot(10, &loop, &QEventLoop::quit);
                loop.exec();
                continue;
            } else if (sslError == SSL_ERROR_ZERO_RETURN) {
                emit debugLog("[UltraFastTLS] 🔚 连接被服务器关闭");
                break;
            } else {
                emit debugLog(QString("[UltraFastTLS] ❌ SSL读取错误: %1").arg(sslError));
                break;
            }
        }

        // 接收到数据
        retryCount = 0; // 重置重试计数

        buffer[received] = '\0';
        // 对于HTTP头部使用UTF-8，对于响应体保持原始字节
        if (!headerComplete) {
            response.append(QString::fromUtf8(buffer, received));
        } else {
            // 响应体部分，保持原始字节数据
            response.append(QString::fromLatin1(buffer, received));
        }

        // 检查HTTP头部是否完整
        if (!headerComplete) {
            int headerEnd = response.indexOf("\r\n\r\n");
            if (headerEnd != -1) {
                headerComplete = true;
                headerLength = headerEnd + 4;

                // 解析Content-Length
                QRegularExpression contentLengthRegex("Content-Length:\\s*(\\d+)", QRegularExpression::CaseInsensitiveOption);
                QRegularExpressionMatch match = contentLengthRegex.match(response);
                if (match.hasMatch()) {
                    contentLength = match.captured(1).toInt();
                }
            }
        }

        // 检查是否接收完整
        if (headerComplete) {
            if (contentLength >= 0) {
                int currentBodyLength = response.length() - headerLength;
                if (currentBodyLength >= contentLength) {
                    break; // 接收完整
                }
            } else {
                // 没有Content-Length，检查是否是chunked编码或连接关闭
                if (response.contains("Transfer-Encoding: chunked", Qt::CaseInsensitive)) {
                    if (response.endsWith("0\r\n\r\n")) {
                        break; // chunked编码结束
                    }
                } else {
                    // 简单情况：假设已接收完整（可以根据需要改进）
                    break;
                }
            }
        }
    }

    // 只返回响应体，去掉HTTP头部
    if (headerComplete && headerLength > 0) {
        QString headers = response.left(headerLength);
        QString body = response.mid(headerLength);
        // 提取响应体

        // 检查是否是gzip压缩
        if (headers.contains("Content-Encoding: gzip", Qt::CaseInsensitive)) {
            emit debugLog("[UltraFastTLS] 🗜️ 检测到gzip压缩，开始解压缩...");
            emit debugLog(QString("[UltraFastTLS] 🔍 压缩数据长度: %1").arg(body.length()));

            // 将QString转换为QByteArray，保持原始字节
            QByteArray compressedBytes = body.toLatin1();
            emit debugLog(QString("[UltraFastTLS] 🔍 压缩字节长度: %1").arg(compressedBytes.length()));

            QString decompressed = decompressGzip(compressedBytes);
            if (!decompressed.isEmpty()) {
                emit debugLog(QString("[UltraFastTLS] ✅ gzip解压缩成功，解压后长度: %1").arg(decompressed.length()));
                emit debugLog(QString("[UltraFastTLS] 📄 解压后内容预览: %1").arg(decompressed.left(100)));
                return decompressed;
            } else {
                emit debugLog("[UltraFastTLS] ❌ gzip解压缩失败，尝试返回原始响应体");
                emit debugLog(QString("[UltraFastTLS] 🔍 原始响应体预览: %1").arg(body.left(100)));
                return body;
            }
        }

        return body;
    }

    // 如果没有找到完整的HTTP头部，返回原始响应
    emit debugLog("[UltraFastTLS] ⚠️ 未找到完整HTTP头部，返回原始响应");
    emit debugLog(QString("[UltraFastTLS] 🔍 原始响应长度: %1").arg(response.length()));
    if (!response.isEmpty()) {
        emit debugLog(QString("[UltraFastTLS] 🔍 原始响应预览: %1").arg(response.left(200)));
    }
    return response;
}

UltraFastTLS::URLInfo UltraFastTLS::parseURL(const QString &url)
{
    URLInfo info;
    QUrl qurl(url);

    info.scheme = qurl.scheme();
    info.host = qurl.host();
    info.port = qurl.port(443); // 默认HTTPS端口
    info.path = qurl.path();
    if (qurl.hasQuery()) {
        info.path += "?" + qurl.query();
    }

    if (info.path.isEmpty()) {
        info.path = "/";
    }

    return info;
}

QString UltraFastTLS::decompressGzip(const QByteArray &compressedData)
{
    if (compressedData.isEmpty()) {
        return QString();
    }

    emit debugLog("[UltraFastTLS] 🔄 开始gzip解压缩...");

    // 检查gzip魔数
    if (compressedData.size() < 10 ||
        (unsigned char)compressedData[0] != 0x1f ||
        (unsigned char)compressedData[1] != 0x8b) {
        emit debugLog("[UltraFastTLS] ❌ 不是有效的gzip格式，返回原始数据");
        return QString::fromUtf8(compressedData);
    }

    emit debugLog("[UltraFastTLS] 🔍 检测到gzip格式，尝试简单解压缩...");

    // 尝试不同的头部跳过长度
    for (int skip = 10; skip <= 18 && skip < compressedData.size() - 8; skip++) {
        QByteArray deflateData = compressedData.mid(skip, compressedData.size() - skip - 8);
        QByteArray result = qUncompress(deflateData);
        if (!result.isEmpty()) {
            emit debugLog(QString("[UltraFastTLS] ✅ 解压缩成功，跳过%1字节，结果长度: %2").arg(skip).arg(result.length()));
            return QString::fromUtf8(result);
        }
    }

    emit debugLog("[UltraFastTLS] ❌ gzip解压缩失败，返回原始数据");
    return QString::fromUtf8(compressedData);
}

void UltraFastTLS::onKeepAliveTimer()
{
    // 发送keep-alive心跳包到活跃连接并检查健康状态
    QMutexLocker locker(&m_poolMutex);

    int activeCount = 0;
    int unhealthyCount = 0;

    for (auto& conn : m_connectionPool) {
        if (!conn->inUse && conn->ssl) {
            // 实验：禁用健康检查，强制保持所有连接有效
            // if (isConnectionHealthy(conn.get())) {
                activeCount++;
            // } else {
            //     emit debugLog(QString("[UltraFastTLS] 🔄 检测到不健康连接，标记为无效: %1:%2")
            //                  .arg(conn->serverHost).arg(conn->serverPort));
            //     conn->isValid = false;
            //     unhealthyCount++;
            // }
            NEW_LOG_INFO(NewLogCategory::NETWORK, "🧪 实验：跳过健康检查，保持连接有效");
        }
    }

    if (unhealthyCount > 0) {
        emit debugLog(QString("[UltraFastTLS] 📊 健康检查完成，发现 %1 个不健康连接").arg(unhealthyCount));
    }

    QMutexLocker statsLocker(&m_statsMutex);
    m_stats.activeConnections = activeCount;
}

void UltraFastTLS::onConnectionCleanup()
{
    cleanupExpiredConnections();
}

void UltraFastTLS::cleanupExpiredConnections()
{
    QMutexLocker locker(&m_poolMutex);

    auto it = m_connectionPool.begin();
    while (it != m_connectionPool.end()) {
        if (!(*it)->inUse && (*it)->isExpired(300)) { // 5分钟超时
            emit debugLog(QString("[UltraFastTLS] 清理过期连接: %1:%2")
                         .arg((*it)->serverHost).arg((*it)->serverPort));
            it = m_connectionPool.erase(it);
        } else {
            ++it;
        }
    }

    emit debugLog(QString("[UltraFastTLS] 连接池状态: %1个连接")
                 .arg(m_connectionPool.size()));
}

bool UltraFastTLS::setupQuarkFallbackFingerprint(SSL* ssl)
{
    // ============================================================
    // 夸克浏览器备用指纹 (简化版本，用于兼容性)
    // ============================================================

    emit debugLog("[夸克备用] 🔄 设置夸克浏览器备用指纹...");

    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 简化的密码套件 (保持核心兼容性)
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"
        "TLS_AES_256_GCM_SHA384:"
        "TLS_CHACHA20_POLY1305_SHA256:"
        "ECDHE-ECDSA-AES128-GCM-SHA256:"
        "ECDHE-RSA-AES128-GCM-SHA256:"
        "ECDHE-ECDSA-AES256-GCM-SHA384:"
        "ECDHE-RSA-AES256-GCM-SHA384:"
        "AES128-GCM-SHA256:"
        "AES256-GCM-SHA384";

    SSL_set_cipher_list(ssl, cipher_list);
    SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

    // 基本椭圆曲线支持
    const int curves[] = { NID_X9_62_prime256v1, NID_secp384r1, NID_X25519 };
    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // ALPN协议
    const unsigned char alpn_protos[] = {
        2, 'h', '2',
        8, 'h', 't', 't', 'p', '/', '1', '.', '1'
    };
    SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos));

    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);

    emit debugLog("[夸克备用] ✅ 夸克浏览器备用指纹设置完成");
    return true;
}

bool UltraFastTLS::setupWechatBrowserFingerprint(SSL* ssl)
{
    // ============================================================
    // 微信浏览器 8.0.61.2880 真实TLS指纹伪装
    // 基于真实抓包数据 JA3: 79e2c4451f525f5cfc10860a9eb180aa
    // JA3文本: 771,4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53,18-65281-17513-27-35-43-10-11-13-5-16-65037-0-45-23-51,4588-29-23-24,0
    // ============================================================

    emit debugLog("[微信指纹] 🔄 设置微信浏览器真实指纹...");
    emit debugLog("[微信指纹] 📱 模拟设备: Android 15 + Chrome 134 + 微信 8.0.61.2880");

    // 1. TLS版本设置 (基于JA3: 771)
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 2. 密码套件设置 (基于真实JA3指纹数据)
    // 精确匹配微信WebView的密码套件顺序
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"           // 4865
        "TLS_AES_256_GCM_SHA384:"           // 4866
        "TLS_CHACHA20_POLY1305_SHA256:"     // 4867
        "ECDHE-ECDSA-AES128-GCM-SHA256:"    // 49195
        "ECDHE-ECDSA-AES256-GCM-SHA384:"    // 49199
        "ECDHE-RSA-AES128-GCM-SHA256:"      // 49196
        "ECDHE-RSA-AES256-GCM-SHA384:"      // 49200
        "ECDHE-PSK-CHACHA20-POLY1305:"      // 52393
        "ECDHE-PSK-AES256-GCM-SHA384:"      // 52392
        "ECDHE-RSA-AES128-SHA256:"          // 49171
        "ECDHE-RSA-AES256-SHA384:"          // 49172
        "AES128-GCM-SHA256:"                // 156
        "AES256-GCM-SHA384:"                // 157
        "AES128-SHA:"                       // 47
        "AES256-SHA";                       // 53

    if (!SSL_set_cipher_list(ssl, cipher_list)) {
        emit debugLog("[微信指纹] ❌ 密码套件设置失败");
        return false;
    }

    // 3. TLS 1.3密码套件 (精确匹配)
    if (!SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256")) {
        emit debugLog("[微信指纹] ⚠️ TLS 1.3密码套件设置失败，使用默认套件");
    }

    // 4. 椭圆曲线设置 (基于JA3: 4588,29,23,24)
    const int curves[] = {
        4588,                       // 4588 (特殊曲线)
        NID_X9_62_prime256v1,      // 23 (P-256)
        NID_secp384r1,             // 24 (P-384)
        NID_X25519,                // 29 (X25519)
    };
    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // 5. ALPN协议设置 (HTTP/2优先，匹配微信WebView)
    const unsigned char alpn_protos[] = {
        2, 'h', '2',        // HTTP/2
        8, 'h', 't', 't', 'p', '/', '1', '.', '1'  // HTTP/1.1
    };
    SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos));

    // 6. 其他TLS设置
    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);

    emit debugLog("[微信指纹] ✅ 微信浏览器真实指纹设置完成");
    emit debugLog("[微信指纹] 🎯 JA3: 79e2c4451f525f5cfc10860a9eb180aa");
    return true;
}

bool UltraFastTLS::setupQuarkAndroid14Fingerprint(SSL* ssl)
{
    // ============================================================
    // 夸克浏览器 7.14.5.880 Android 14 真实TLS指纹伪装
    // 基于真实抓包数据 JA3: 4c87eb5f587bf477c55677398fa9fbe2
    // JA3文本: 771,4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53,17513-5-0-16-65281-23-51-11-18-27-10-35-45-43-13-21,29-23-24,0
    // ============================================================

    emit debugLog("[夸克Android14] 🔄 设置夸克浏览器Android 14真实指纹...");
    emit debugLog("[夸克Android14] 📱 模拟设备: Android 14 + Chrome 100 + 夸克 7.14.5.880");

    // 1. TLS版本设置 (基于JA3: 771)
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 2. 密码套件设置 (基于真实JA3指纹数据)
    // 精确匹配夸克浏览器Android 14的密码套件顺序
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"           // 4865
        "TLS_AES_256_GCM_SHA384:"           // 4866
        "TLS_CHACHA20_POLY1305_SHA256:"     // 4867
        "ECDHE-ECDSA-AES128-GCM-SHA256:"    // 49195
        "ECDHE-ECDSA-AES256-GCM-SHA384:"    // 49199
        "ECDHE-RSA-AES128-GCM-SHA256:"      // 49196
        "ECDHE-RSA-AES256-GCM-SHA384:"      // 49200
        "ECDHE-PSK-CHACHA20-POLY1305:"      // 52393
        "ECDHE-PSK-AES256-GCM-SHA384:"      // 52392
        "ECDHE-RSA-AES128-SHA256:"          // 49171
        "ECDHE-RSA-AES256-SHA384:"          // 49172
        "AES128-GCM-SHA256:"                // 156
        "AES256-GCM-SHA384:"                // 157
        "AES128-SHA:"                       // 47
        "AES256-SHA";                       // 53

    if (!SSL_set_cipher_list(ssl, cipher_list)) {
        emit debugLog("[夸克Android14] ❌ 密码套件设置失败");
        return false;
    }

    // 3. TLS 1.3密码套件 (精确匹配)
    if (!SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256")) {
        emit debugLog("[夸克Android14] ⚠️ TLS 1.3密码套件设置失败，使用默认套件");
    }

    // 4. 椭圆曲线设置 (基于JA3: 29,23,24)
    const int curves[] = {
        NID_X25519,                // 29 (X25519)
        NID_X9_62_prime256v1,     // 23 (P-256)
        NID_secp384r1,            // 24 (P-384)
    };
    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // 5. ALPN协议设置 (HTTP/1.1优先，基于JA4标识)
    const unsigned char alpn_protos[] = {
        8, 'h', 't', 't', 'p', '/', '1', '.', '1',  // HTTP/1.1
        2, 'h', '2'                                 // HTTP/2
    };
    SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos));

    // 6. 其他TLS设置
    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);

    emit debugLog("[夸克Android14] ✅ 夸克浏览器Android 14真实指纹设置完成");
    emit debugLog("[夸克Android14] 🎯 JA3: 4c87eb5f587bf477c55677398fa9fbe2");
    return true;
}

// ==================== 缺失函数的简单实现 ====================

UltraFastTLS::ParsedUrlInfo UltraFastTLS::parseUrlString(const QString &urlString)
{
    ParsedUrlInfo info;
    QUrl url(urlString);

    if (url.isValid()) {
        info.hostName = url.host();
        info.portNumber = url.port(url.scheme() == "https" ? 443 : 80);
        info.path = url.path();
        if (info.path.isEmpty()) info.path = "/";
        info.query = url.query();
        info.isHttps = (url.scheme() == "https");
    }

    return info;
}

QString UltraFastTLS::executeSingleHttpRequest(ConnectionInfo* connectionInfo,
                                             const ParsedUrlInfo& parsedUrl,
                                             const QString& postData)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        emit debugLog("[UltraFastTLS] ❌ 无效的连接信息");
        return QString();
    }

    emit debugLog(QString("[UltraFastTLS] 🚀 开始执行HTTP请求: %1").arg(parsedUrl.hostName));

    // 构建HTTP请求
    QString httpRequest = buildHTTP11Request(
        postData.isEmpty() ? "GET" : "POST",
        parsedUrl.path + (parsedUrl.query.isEmpty() ? "" : "?" + parsedUrl.query),
        parsedUrl.hostName,
        postData
    );

    emit debugLog(QString("[UltraFastTLS] 📝 HTTP请求构建完成，长度: %1").arg(httpRequest.length()));

    // 发送HTTP请求
    if (!sendHttpRequest(connectionInfo, httpRequest)) {
        emit debugLog("[UltraFastTLS] ❌ 发送HTTP请求失败");
        connectionInfo->isValid = false;
        return QString();
    }

    // 接收HTTP响应
    QString response = receiveHttpResponse(connectionInfo);
    if (response.isEmpty()) {
        emit debugLog("[UltraFastTLS] ❌ 接收HTTP响应失败");
        connectionInfo->isValid = false;
        return QString();
    }

    // 解析HTTP响应并检测keep-alive
    QString body = parseHttpResponse(response, connectionInfo);
    if (body.isEmpty()) {
        emit debugLog("[UltraFastTLS] ⚠️ HTTP响应体为空");
    } else {
        emit debugLog(QString("[UltraFastTLS] ✅ HTTP请求成功，响应长度: %1").arg(body.length()));
    }

    // 更新连接使用时间
    connectionInfo->lastUsed = std::chrono::steady_clock::now();

    return body;
}

QString UltraFastTLS::handleRequestRetry(const ParsedUrlInfo& parsedUrl,
                                        const QString& httpRequest)
{
    // 简化实现：返回空字符串表示重试失败
    Q_UNUSED(parsedUrl)
    Q_UNUSED(httpRequest)

    emit debugLog("[UltraFastTLS] handleRequestRetry - 简化实现");
    return QString();
}

void UltraFastTLS::updateRequestPerformanceStats(const std::chrono::high_resolution_clock::time_point& startTime,
                                                bool success,
                                                int responseSize)
{
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    emit debugLog(QString("[UltraFastTLS] 性能统计 - 成功: %1, 响应大小: %2, 耗时: %3ms")
                  .arg(success ? "是" : "否")
                  .arg(responseSize)
                  .arg(duration.count()));
}

// 发送HTTP请求到TLS连接
bool UltraFastTLS::sendHttpRequest(ConnectionInfo* connectionInfo, const QString& httpRequest)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        return false;
    }

    QByteArray requestData = httpRequest.toUtf8();
    emit debugLog(QString("[UltraFastTLS] 📤 发送HTTP请求，大小: %1 字节").arg(requestData.size()));

#ifdef OPENSSL_FOUND
    if (!connectionInfo->ssl) {
        emit debugLog("[UltraFastTLS] ❌ SSL连接未初始化");
        return false;
    }

    int totalSent = 0;
    int remaining = requestData.size();
    const char* data = requestData.constData();

    while (remaining > 0) {
        int sent = SSL_write(connectionInfo->ssl, data + totalSent, remaining);
        if (sent <= 0) {
            int error = SSL_get_error(connectionInfo->ssl, sent);
            emit debugLog(QString("[UltraFastTLS] ❌ SSL_write失败，错误码: %1").arg(error));
            return false;
        }
        totalSent += sent;
        remaining -= sent;
    }

    emit debugLog(QString("[UltraFastTLS] ✅ HTTP请求发送完成，共发送: %1 字节").arg(totalSent));
    return true;
#else
    // Windows SChannel实现
    emit debugLog("[UltraFastTLS] ⚠️ Windows SChannel发送暂未实现");
    return false;
#endif
}

// 接收HTTP响应
QString UltraFastTLS::receiveHttpResponse(ConnectionInfo* connectionInfo)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        return QString();
    }

    emit debugLog("[UltraFastTLS] 📥 开始接收HTTP响应");

#ifdef OPENSSL_FOUND
    if (!connectionInfo->ssl) {
        emit debugLog("[UltraFastTLS] ❌ SSL连接未初始化");
        return QString();
    }

    QByteArray responseData;
    char buffer[8192];
    bool headerReceived = false;
    int contentLength = -1;
    int headerEndPos = -1;

    // 接收响应数据
    while (true) {
        int received = SSL_read(connectionInfo->ssl, buffer, sizeof(buffer) - 1);
        if (received <= 0) {
            int error = SSL_get_error(connectionInfo->ssl, received);
            if (error == SSL_ERROR_WANT_READ || error == SSL_ERROR_WANT_WRITE) {
                // 需要更多数据，继续等待
                QThread::msleep(10);
                continue;
            } else {
                // 根据错误码提供更准确的描述和处理
                if (error == SSL_ERROR_ZERO_RETURN) {
                    // 这是正常的连接关闭，不需要报告为错误
                    if (responseData.isEmpty()) {
                        emit debugLog("[UltraFastTLS] ⚠️ 服务器在发送数据前关闭连接，可能需要重试");
                    } else {
                        // 有数据接收到，这是正常的
                        emit debugLog("[UltraFastTLS] ✅ 数据接收完成，服务器关闭连接");
                    }
                } else {
                    // 智能错误过滤：只报告真正需要关注的错误
                    QString errorDetail = getSSLErrorDescription(error);
                    bool shouldReportError = true;

                    // 智能分析错误严重程度
                    if (error == SSL_ERROR_SSL) {
                        unsigned long opensslError = ERR_peek_error();
                        if (ERR_GET_REASON(opensslError) == SSL_R_UNEXPECTED_EOF_WHILE_READING) {
                            // 分析连接关闭的详细情况
                            bool dataReceived = !responseData.isEmpty();
                            auto connectionAge = connectionInfo ?
                                std::chrono::duration_cast<std::chrono::milliseconds>(
                                    std::chrono::steady_clock::now() - connectionInfo->lastUsed).count() : 0;

                            m_stats.normalClosures++;
                            shouldReportError = false; // 不报告为异常

                            // 实验：不标记连接为无效，强制一直复用
                            if (connectionInfo) {
                                // connectionInfo->isValid = false; // 注释掉，强制复用
                                NEW_LOG_INFO(NewLogCategory::NETWORK, "🧪 实验：保持连接有效，不标记为无效");
                            }

                            // 虚假keep-alive检测
                            bool quickClose = connectionAge < 500; // 500ms内关闭算"快速关闭"
                            bool hadKeepAlive = connectionInfo && connectionInfo->serverSaidKeepAlive;

                            if (hadKeepAlive && quickClose) {
                                // 检测到虚假keep-alive！
                                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("🕵️ 虚假keep-alive检测: 服务器声明keep-alive但%1ms后就关闭连接").arg(connectionAge));

                                // 记录虚假keep-alive次数
                                m_stats.fakeKeepAliveCount++;

                                if (m_stats.fakeKeepAliveCount >= 3) {
                                    NEW_LOG_WARNING(NewLogCategory::NETWORK, "🚨 服务器多次虚假keep-alive，建议禁用连接复用");
                                }
                            } else if (!m_quietMode) {
                                if (dataReceived) {
                                    emit debugLog(QString("[UltraFastTLS] 📋 连接分析: 服务器在数据发送完成后关闭连接 (存活%1ms)").arg(connectionAge));
                                } else {
                                    emit debugLog(QString("[UltraFastTLS] ⚠️ 连接分析: 服务器在数据发送前就关闭了连接 (存活%1ms)").arg(connectionAge));
                                }
                            }
                        }
                    }

                    // 只报告真正需要关注的错误
                    if (shouldReportError) {
                        // 在安静模式下，进一步减少错误报告
                        if (!m_quietMode) {
                            emit debugLog(QString("[UltraFastTLS] ❌ 连接异常关闭，错误码: %1 (%2)").arg(error).arg(errorDetail));

                            // 对于SSL_ERROR_SSL (错误码1)，尝试获取更详细的错误信息
                            if (error == SSL_ERROR_SSL) {
                                unsigned long opensslError = ERR_get_error();
                                if (opensslError != 0) {
                                    char errorBuffer[256];
                                    ERR_error_string_n(opensslError, errorBuffer, sizeof(errorBuffer));
                                    emit debugLog(QString("[UltraFastTLS] 🔍 详细SSL错误: %1").arg(QString::fromUtf8(errorBuffer)));
                                }
                            }
                        }
                    }

                    // 实验：不标记连接为无效，强制一直复用
                    if (connectionInfo) {
                        // connectionInfo->isValid = false; // 注释掉，强制复用
                        NEW_LOG_INFO(NewLogCategory::NETWORK, "🧪 实验：即使有错误也保持连接有效");
                    }
                }
                break;
            }
        }

        buffer[received] = '\0';
        responseData.append(buffer, received);

        // 检查是否接收到完整的HTTP头
        if (!headerReceived) {
            headerEndPos = responseData.indexOf("\r\n\r\n");
            if (headerEndPos != -1) {
                headerReceived = true;
                QString headers = QString::fromUtf8(responseData.left(headerEndPos));
                contentLength = extractContentLength(headers);
                emit debugLog(QString("[UltraFastTLS] 📋 HTTP头接收完成，Content-Length: %1").arg(contentLength));
            }
        }

        // 如果已知内容长度，检查是否接收完整
        if (headerReceived && contentLength >= 0) {
            int bodyStart = headerEndPos + 4;
            int bodyReceived = responseData.size() - bodyStart;
            if (bodyReceived >= contentLength) {
                emit debugLog(QString("[UltraFastTLS] ✅ HTTP响应接收完成，总大小: %1 字节").arg(responseData.size()));
                break;
            }
        }

        // 防止无限接收
        if (responseData.size() > 10 * 1024 * 1024) { // 10MB限制
            emit debugLog("[UltraFastTLS] ⚠️ 响应数据过大，停止接收");
            break;
        }
    }

    return QString::fromUtf8(responseData);
#else
    // Windows SChannel实现
    emit debugLog("[UltraFastTLS] ⚠️ Windows SChannel接收暂未实现");
    return QString();
#endif
}

// 解析HTTP响应，提取响应体并检测keep-alive
QString UltraFastTLS::parseHttpResponse(const QString& response, ConnectionInfo* connectionInfo)
{
    if (response.isEmpty()) {
        return QString();
    }

    // 查找HTTP头结束位置
    int headerEndPos = response.indexOf("\r\n\r\n");
    if (headerEndPos == -1) {
        emit debugLog("[UltraFastTLS] ❌ 无效的HTTP响应格式");
        return QString();
    }

    // 提取HTTP头和响应体
    QString headers = response.left(headerEndPos);
    QString body = response.mid(headerEndPos + 4);

    // 检测服务器是否声明了keep-alive
    if (connectionInfo) {
        connectionInfo->serverSaidKeepAlive = headers.contains("Connection: keep-alive", Qt::CaseInsensitive);

        // 强制输出keep-alive检测结果（使用新日志系统）
        if (connectionInfo->serverSaidKeepAlive) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, "🔍 Keep-Alive检测: 服务器声明 Connection: keep-alive");
        } else {
            NEW_LOG_INFO(NewLogCategory::NETWORK, "🔍 Keep-Alive检测: 服务器未声明keep-alive或声明close");
        }
    }

    // 检查HTTP状态码
    QStringList headerLines = headers.split("\r\n");
    if (headerLines.isEmpty()) {
        emit debugLog("[UltraFastTLS] ❌ 无效的HTTP状态行");
        return QString();
    }

    QString statusLine = headerLines.first();
    if (!statusLine.contains("200")) {
        emit debugLog(QString("[UltraFastTLS] ⚠️ HTTP状态: %1").arg(statusLine));
        // 即使不是200状态码，也返回响应体，让上层处理
    }

    // 检查是否是chunked编码
    if (headers.contains("Transfer-Encoding: chunked", Qt::CaseInsensitive)) {
        body = decodeChunkedResponse(body);
        emit debugLog("[UltraFastTLS] 🔄 已解码chunked响应");
    }

    // 检查是否是gzip压缩
    if (headers.contains("Content-Encoding: gzip", Qt::CaseInsensitive)) {
        body = decompressGzipResponse(body);
        emit debugLog("[UltraFastTLS] 🗜️ 已解压gzip响应");
    }

    emit debugLog(QString("[UltraFastTLS] ✅ HTTP响应解析完成，响应体长度: %1").arg(body.length()));
    return body;
}

// 提取Content-Length头
int UltraFastTLS::extractContentLength(const QString& headers)
{
    QStringList headerLines = headers.split("\r\n");
    for (const QString& line : headerLines) {
        if (line.startsWith("Content-Length:", Qt::CaseInsensitive)) {
            QString lengthStr = line.mid(15).trimmed();
            bool ok;
            int length = lengthStr.toInt(&ok);
            return ok ? length : -1;
        }
    }
    return -1;
}

// 解码chunked传输编码
QString UltraFastTLS::decodeChunkedResponse(const QString& chunkedData)
{
    QByteArray data = chunkedData.toUtf8();
    QByteArray result;
    int pos = 0;

    while (pos < data.size()) {
        // 查找chunk大小行的结束
        int crlfPos = data.indexOf("\r\n", pos);
        if (crlfPos == -1) break;

        // 解析chunk大小（十六进制）
        QString chunkSizeStr = QString::fromUtf8(data.mid(pos, crlfPos - pos));
        bool ok;
        int chunkSize = chunkSizeStr.toInt(&ok, 16);
        if (!ok) {
            emit debugLog(QString("[UltraFastTLS] ❌ 无效的chunk大小: %1").arg(chunkSizeStr));
            break;
        }

        if (chunkSize == 0) {
            // 最后一个chunk
            emit debugLog("[UltraFastTLS] ✅ chunked解码完成");
            break;
        }

        // 跳过CRLF，读取chunk数据
        pos = crlfPos + 2;
        if (pos + chunkSize > data.size()) {
            emit debugLog("[UltraFastTLS] ❌ chunk数据不完整");
            break;
        }

        result.append(data.mid(pos, chunkSize));
        pos += chunkSize + 2; // 跳过chunk数据和结尾的CRLF
    }

    return QString::fromUtf8(result);
}

// 解压gzip响应
QString UltraFastTLS::decompressGzipResponse(const QString& gzipData)
{
#ifdef HAS_ZLIB
    QByteArray compressed = gzipData.toUtf8();
    QByteArray decompressed;

    z_stream stream;
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;
    stream.avail_in = compressed.size();
    stream.next_in = (Bytef*)compressed.data();

    // 初始化gzip解压
    if (inflateInit2(&stream, 16 + MAX_WBITS) != Z_OK) {
        emit debugLog("[UltraFastTLS] ❌ gzip解压初始化失败");
        return gzipData; // 返回原始数据
    }

    char buffer[8192];
    int ret;
    do {
        stream.avail_out = sizeof(buffer);
        stream.next_out = (Bytef*)buffer;
        ret = inflate(&stream, Z_NO_FLUSH);
        if (ret == Z_STREAM_ERROR) break;
        decompressed.append(buffer, sizeof(buffer) - stream.avail_out);
    } while (stream.avail_out == 0);

    inflateEnd(&stream);

    if (ret == Z_STREAM_END) {
        emit debugLog(QString("[UltraFastTLS] ✅ gzip解压成功，原始: %1 → 解压: %2")
                     .arg(compressed.size()).arg(decompressed.size()));
        return QString::fromUtf8(decompressed);
    } else {
        emit debugLog("[UltraFastTLS] ❌ gzip解压失败");
        return gzipData; // 返回原始数据
    }
#else
    emit debugLog("[UltraFastTLS] ⚠️ zlib不可用，跳过gzip解压");
    return gzipData; // 返回原始数据
#endif
}

// SSL错误描述函数
QString UltraFastTLS::getSSLErrorDescription(int sslError)
{
    switch (sslError) {
        case SSL_ERROR_NONE:
            return "无错误";
        case SSL_ERROR_SSL:
            return "SSL协议错误 - 可能是证书问题或协议不匹配";
        case SSL_ERROR_WANT_READ:
            return "需要更多数据读取";
        case SSL_ERROR_WANT_WRITE:
            return "需要更多数据写入";
        case SSL_ERROR_WANT_X509_LOOKUP:
            return "X509证书查找错误";
        case SSL_ERROR_SYSCALL:
            return "系统调用错误 - 可能是网络中断";
        case SSL_ERROR_ZERO_RETURN:
            return "连接被对方正常关闭";
        case SSL_ERROR_WANT_CONNECT:
            return "连接未完成";
        case SSL_ERROR_WANT_ACCEPT:
            return "接受未完成";
        default:
            return QString("未知SSL错误 (%1)").arg(sslError);
    }
}

// 检查连接健康状态
bool UltraFastTLS::isConnectionHealthy(ConnectionInfo* conn)
{
    if (!conn || !conn->ssl || !conn->isValid) {
        return false;
    }

#ifdef OPENSSL_FOUND
    // 检查SSL连接状态
    int sslState = SSL_get_shutdown(conn->ssl);
    if (sslState != 0) {
        // 连接已经关闭或正在关闭
        return false;
    }

    // 尝试一个非阻塞的SSL_peek来检查连接是否还活着
    char testByte;
    int peekResult = SSL_peek(conn->ssl, &testByte, 1);
    if (peekResult < 0) {
        int error = SSL_get_error(conn->ssl, peekResult);
        if (error != SSL_ERROR_WANT_READ && error != SSL_ERROR_WANT_WRITE) {
            // 连接有问题
            return false;
        }
    }

    return true;
#else
    // Windows SChannel健康检查
    return conn->isValid;
#endif
}

// 智能重试机制
void UltraFastTLS::cleanupStaleConnections()
{
    QMutexLocker locker(&m_poolMutex);

    auto now = std::chrono::steady_clock::now();
    auto it = m_connectionPool.begin();

    while (it != m_connectionPool.end()) {
        auto& conn = *it;
        auto age = std::chrono::duration_cast<std::chrono::seconds>(now - conn->lastUsed).count();

        // 清理超过3秒未使用或已标记为无效的连接
        if (!conn->isValid || age > 3) {
            it = m_connectionPool.erase(it);
        } else {
            ++it;
        }
    }
}

QString UltraFastTLS::handleSmartRetry(const ParsedUrlInfo& parsedUrl, const QString& httpRequest)
{
    const int maxRetries = 3;
    const QList<int> retryDelays = {100, 300, 1000}; // 递增延迟：100ms, 300ms, 1000ms

    for (int attempt = 1; attempt <= maxRetries; ++attempt) {
        emit debugLog(QString("[UltraFastTLS] 🔄 智能重试第 %1/%2 次").arg(attempt).arg(maxRetries));

        // 等待递增延迟
        if (attempt > 1) {
            int delay = retryDelays[attempt - 2];
            emit debugLog(QString("[UltraFastTLS] ⏳ 等待 %1ms 后重试").arg(delay));
            QEventLoop loop;
            QTimer::singleShot(delay, &loop, &QEventLoop::quit);
            loop.exec();
        }

        // 创建新连接进行重试
        ConnectionInfo* retryConnection = borrowConnection(parsedUrl.hostName, parsedUrl.portNumber);
        if (!retryConnection) {
            emit debugLog(QString("[UltraFastTLS] ❌ 重试第 %1 次失败：无法获取连接").arg(attempt));
            continue;
        }

        // 执行重试请求
        QString response = executeSingleHttpRequest(retryConnection, parsedUrl,
                                                   httpRequest.contains("POST") ?
                                                   extractPostDataFromRequest(httpRequest) : "");

        // 归还连接
        if (response.isEmpty()) {
            retryConnection->isValid = false;
        }
        returnConnection(retryConnection);

        if (!response.isEmpty()) {
            emit debugLog(QString("[UltraFastTLS] ✅ 智能重试第 %1 次成功").arg(attempt));
            return response;
        }

        emit debugLog(QString("[UltraFastTLS] ❌ 智能重试第 %1 次失败").arg(attempt));
    }

    emit debugLog("[UltraFastTLS] ❌ 所有智能重试均失败");
    return QString();
}

// 从HTTP请求中提取POST数据
QString UltraFastTLS::extractPostDataFromRequest(const QString& httpRequest)
{
    // 查找HTTP头部和正文的分隔符
    int separatorPos = httpRequest.indexOf("\r\n\r\n");
    if (separatorPos == -1) {
        return QString();
    }

    // 返回正文部分
    return httpRequest.mid(separatorPos + 4);
}

