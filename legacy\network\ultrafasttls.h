#ifndef ULTRAFASTTLS_H
#define ULTRAFASTTLS_H

#include <QString>
#include <QObject>
#include <QMutex>
#include <QThread>
#include <QTimer>
#include <vector>
#include <memory>
#include <chrono>

// 前向声明调试监控器
class UltraFastTLSDebugMonitor;

// 尝试包含zlib，如果不可用则使用备用方案
#ifdef HAS_ZLIB
#include <zlib.h>
#endif

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>

#ifdef OPENSSL_FOUND
#include <openssl/ssl.h>
#include <openssl/err.h>
#include <openssl/crypto.h>
#include <openssl/evp.h>
#include <openssl/x509.h>
#include <openssl/x509v3.h>
#include <openssl/ocsp.h>
#include <openssl/obj_mac.h>
#else
// Windows原生TLS (SChannel)
#define SECURITY_WIN32
#include <schannel.h>
#include <security.h>
#include <sspi.h>
#include <secext.h>
#endif

#ifdef _MSC_VER
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "crypt32.lib")

#ifndef OPENSSL_FOUND
#pragma comment(lib, "secur32.lib")
#pragma comment(lib, "schannel.lib")
#endif
#endif

#endif

/**
 * @class UltraFastTLS
 * @brief 超高性能TLS网络请求引擎
 *
 * 这是一个专门为高频网络请求优化的TLS客户端实现，具有以下特点：
 *
 * @par 性能优势
 * - 比标准Qt网络库快3-5倍
 * - 预建立连接池，减少握手开销
 * - HTTP/1.1持久连接支持
 * - 零拷贝I/O操作
 *
 * @par 安全特性
 * - 完美夸克浏览器TLS指纹伪装
 * - 支持现代TLS 1.2/1.3协议
 * - 自动证书验证
 *
 * @par 使用示例
 * @code
 * UltraFastTLS* tls = new UltraFastTLS(this);
 * if (tls->initialize()) {
 *     QString response = tls->executeRequest("https://api.example.com/data");
 * }
 * @endcode
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-10-02
 */
class UltraFastTLS : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief URL解析结果结构体
     */
    struct ParsedUrlInfo {
        QString scheme;      // 协议（http/https）
        QString hostName;    // 主机名
        int portNumber = 443; // 端口号
        QString requestPath; // 请求路径
        QString path;        // 路径部分
        QString query;       // 查询参数
        QString fragment;    // 片段标识符
        bool isHttps = true; // 是否为HTTPS
    };

    /**
     * @brief 构造函数
     * @param parent 父对象指针，用于Qt对象树管理
     */
    explicit UltraFastTLS(QObject *parent = nullptr);

    /**
     * @brief 析构函数 - 自动清理所有资源
     */
    ~UltraFastTLS();

    /**
     * @brief 初始化TLS引擎和连接池
     *
     * 执行以下初始化操作：
     * - 初始化SSL/TLS库
     * - 创建连接池
     * - 启动维护定时器
     *
     * @return true 如果初始化成功，false 如果失败
     * @note 必须在使用其他方法前调用此方法
     */
    bool initialize();

    /**
     * @brief 执行HTTP/HTTPS请求
     *
     * 这是主要的请求接口，支持GET和POST请求。
     * 自动处理连接复用、重试和错误恢复。
     *
     * @param url 完整的请求URL（必须是https://开头）
     * @param postData POST数据，为空时执行GET请求
     * @return HTTP响应内容，失败时返回空字符串
     *
     * @par 性能提示
     * - 相同域名的请求会复用连接
     * - 支持gzip压缩自动解压
     * - 内置重试机制
     */
    QString executeRequest(const QString &url, const QString &postData = QString());

    // 设置连接池大小
    void setPoolSize(int size) { m_poolSize = size; }

    /**
     * @enum BrowserFingerprint
     * @brief 浏览器TLS指纹伪装类型
     *
     * 定义不同的浏览器指纹伪装模式，用于绕过服务器的客户端检测。
     */
    enum class BrowserFingerprint {
        /// 夸克浏览器 7.14.5.880 标准模式（推荐）
        QUARK_BROWSER,
        /// 夸克浏览器备用模式（兼容性更好）
        QUARK_FALLBACK,
        /// 微信浏览器 8.0.61.2880 模式（基于真实指纹）
        WECHAT_BROWSER,
        /// 夸克浏览器 Android 14 模式（基于真实指纹）
        QUARK_ANDROID14
    };

    // 设置浏览器指纹类型
    void setBrowserFingerprint(BrowserFingerprint type) { m_browserType = type; }

    /**
     * @struct PerformanceStats
     * @brief 性能统计数据结构
     *
     * 包含详细的性能指标，用于监控和优化网络请求性能。
     */
    struct PerformanceStats {
        /// 总请求次数
        int totalRequests = 0;
        /// 平均延迟（毫秒）
        double averageLatency = 0.0;
        /// 当前活跃连接数
        int activeConnections = 0;
        /// 连接池命中次数（复用连接）
        int poolHits = 0;
        /// 连接池未命中次数（新建连接）
        int poolMisses = 0;
        /// 当前使用的浏览器类型
        QString currentBrowserType;
        /// SSL错误统计
        int sslErrors = 0;
        /// 连接关闭错误（正常情况）
        int normalClosures = 0;
        /// 连接复用失败次数
        int reuseFailures = 0;
        /// 虚假keep-alive检测次数
        int fakeKeepAliveCount = 0;
    };

    // 获取性能统计
    PerformanceStats getStats() const { return m_stats; }

    // 设置安静模式（类似curl的行为）
    void setQuietMode(bool quiet) { m_quietMode = quiet; }
    bool isQuietMode() const { return m_quietMode; }

    // 调试监控器相关
    void setDebugMonitor(UltraFastTLSDebugMonitor* monitor) { m_debugMonitor = monitor; }
    UltraFastTLSDebugMonitor* getDebugMonitor() const { return m_debugMonitor; }
    QString generateDebugReport() const;

signals:
    /**
     * @brief 调试日志信号
     * @param message 调试消息内容
     *
     * 当引擎内部发生重要事件时发出此信号，包括：
     * - 连接建立/断开
     * - 请求执行状态
     * - 错误信息
     */
    void debugLog(const QString &message);

    /**
     * @brief 性能更新信号
     * @param statsInfo 格式化的性能统计信息
     *
     * 定期发出性能统计更新，包含延迟、吞吐量等指标。
     */
    void performanceUpdate(const QString &statsInfo);

private slots:
    /**
     * @brief 连接保活定时器处理槽
     *
     * 定期向服务器发送保活请求，维持连接池中连接的活跃状态。
     */
    void onKeepAliveTimer();

    /**
     * @brief 连接清理定时器处理槽
     *
     * 定期清理过期和无效的连接，释放系统资源。
     */
    void onConnectionCleanup();

private:
    // 连接信息结构
    struct ConnectionInfo {
        SOCKET socket = INVALID_SOCKET;
#ifdef OPENSSL_FOUND
        SSL* ssl = nullptr;
#else
        // Windows SChannel相关
        CtxtHandle sslContext;
        CredHandle credentials;
        SecPkgContext_StreamSizes streamSizes;
        bool sslInitialized = false;
#endif
        bool inUse = false;
        bool isValid = true;  // 连接是否有效
        std::chrono::steady_clock::time_point lastUsed;
        std::chrono::steady_clock::time_point created;
        QString serverHost;
        int serverPort = 443;
        int requestCount = 0;        // 此连接上的请求计数
        bool serverClosed = false;   // 服务器是否已关闭连接
        bool serverSaidKeepAlive = false; // 服务器是否声明了keep-alive
        int reuseCount = 0;          // 连接复用次数

        ConnectionInfo() {
            lastUsed = std::chrono::steady_clock::now();
            created = lastUsed;
        }

        ~ConnectionInfo() {
            cleanup();
        }

        void cleanup() {
#ifdef OPENSSL_FOUND
            if (ssl) {
                SSL_shutdown(ssl);
                SSL_free(ssl);
                ssl = nullptr;
            }
#else
            if (sslInitialized) {
                DeleteSecurityContext(&sslContext);
                FreeCredentialsHandle(&credentials);
                sslInitialized = false;
            }
#endif
            if (socket != INVALID_SOCKET) {
                closesocket(socket);
                socket = INVALID_SOCKET;
            }
        }

        bool isExpired(int timeoutSeconds = 60) const {
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - lastUsed);
            return elapsed.count() > timeoutSeconds;
        }
    };

    /**
     * @brief 初始化SSL/TLS库
     * @return true 如果初始化成功
     */
    bool initializeSslLibrary();

    /**
     * @brief 创建并初始化连接池
     * @return true 如果连接池创建成功
     */
    bool createTlsConnectionPool();

    // 创建单个TLS连接
    std::unique_ptr<ConnectionInfo> createSingleTlsConnection(
        const QString &hostName, int portNumber);

    /**
     * @brief 设置夸克浏览器TLS指纹伪装
     */
#ifdef OPENSSL_FOUND
    bool setupQuarkBrowserTlsFingerprint(SSL* sslContext);
    bool configureQuarkBrowserFingerprint(SSL* sslContext);
    bool configureQuarkFallbackFingerprint(SSL* sslContext);
#else
    bool setupQuarkBrowserTlsFingerprint(TlsConnectionInfo* connectionInfo);
    bool configureQuarkBrowserFingerprint(TlsConnectionInfo* connectionInfo);
    bool configureQuarkFallbackFingerprint(TlsConnectionInfo* connectionInfo);
#endif

    // 从池中借用连接
    ConnectionInfo* borrowConnectionFromPool(const QString &hostName, int portNumber);

    // 归还连接到池
    void returnConnectionToPool(ConnectionInfo* connectionInfo);

    /**
     * @brief 构建HTTP/1.1请求字符串
     * @param httpMethod HTTP方法（GET/POST等）
     * @param requestPath 请求路径
     * @param hostName 主机名
     * @param postData POST数据（可选）
     * @return 完整的HTTP请求字符串
     */
    QString buildHttp11RequestString(const QString &httpMethod, const QString &requestPath,
                                   const QString &hostName, const QString &postData);

    // 发送HTTP请求
    bool sendHttpRequestOverTls(ConnectionInfo* connectionInfo, const QString &httpRequest);

    // 读取HTTP响应
    QString readHttpResponseFromTls(ConnectionInfo* connectionInfo);

    /**
     * @brief 解压缩GZIP数据
     * @param compressedData 压缩的数据
     * @return 解压后的字符串
     */
    QString decompressGzipData(const QByteArray &compressedData);

    /**
     * @brief 获取SSL错误的详细描述
     * @param sslError SSL错误码
     * @return 错误描述字符串
     */
    QString getSSLErrorDescription(int sslError);

    /**
     * @brief 检查连接健康状态
     * @param conn 连接信息
     * @return 连接是否健康
     */
    bool isConnectionHealthy(ConnectionInfo* conn);

    /**
     * @brief 检测服务器SSL复连支持能力
     * @param conn 连接信息
     * @return SSL复连支持信息
     */
    QString detectSSLReconnectSupport(ConnectionInfo* conn);

    /**
     * @brief 智能重试机制
     * @param parsedUrl 解析后的URL信息
     * @param httpRequest HTTP请求内容
     * @return 响应内容
     */
    QString handleSmartRetry(const ParsedUrlInfo& parsedUrl, const QString& httpRequest);

    /**
     * @brief 从HTTP请求中提取POST数据
     * @param httpRequest HTTP请求内容
     * @return POST数据
     */
    QString extractPostDataFromRequest(const QString& httpRequest);



    /**
     * @brief 解析URL字符串
     * @param urlString 要解析的URL
     * @return 解析后的URL信息
     */
    ParsedUrlInfo parseUrlString(const QString &urlString);

    // 初始化SSL库
    bool initializeSSL();

    // 创建连接池
    bool createConnectionPool();

    // 创建单个TLS连接
    std::unique_ptr<ConnectionInfo> createTLSConnection(const QString &host, int port);

    // 清理失效连接
    void cleanupStaleConnections();

    // 设置完美的TLS指纹 (浏览器指纹专用)
#ifdef OPENSSL_FOUND
    bool setupPerfectTLSFingerprint(SSL* ssl);
    bool setupQuarkBrowserFingerprint(SSL* ssl);
    bool setupQuarkFallbackFingerprint(SSL* ssl);
    bool setupWechatBrowserFingerprint(SSL* ssl);
    bool setupQuarkAndroid14Fingerprint(SSL* ssl);
#else
    bool setupPerfectTLSFingerprint(ConnectionInfo* conn);
    bool setupQuarkBrowserFingerprint(ConnectionInfo* conn);
    bool setupQuarkFallbackFingerprint(ConnectionInfo* conn);
    bool setupWechatBrowserFingerprint(ConnectionInfo* conn);
    bool setupQuarkAndroid14Fingerprint(ConnectionInfo* conn);
#endif

    // 从池中借用连接
    ConnectionInfo* borrowConnection(const QString &host, int port);

    // 归还连接到池
    void returnConnection(ConnectionInfo* conn);

    // 构建HTTP/1.1请求
    QString buildHTTP11Request(const QString &method, const QString &path,
                              const QString &host, const QString &postData);

    // 发送HTTP请求
    bool sendHTTPRequest(ConnectionInfo* conn, const QString &request);

    // 读取HTTP响应
    QString readHTTPResponse(ConnectionInfo* conn);

    // gzip解压缩
    QString decompressGzip(const QByteArray &compressedData);

    // 解析URL
    struct URLInfo {
        QString scheme;
        QString host;
        int port = 443;
        QString path;
    };
    URLInfo parseURL(const QString &url);

    // 清理过期连接
    void cleanupExpiredConnections();



    // ========== 模块化辅助方法 ==========

    // 执行单次HTTP请求
    QString executeSingleHttpRequest(ConnectionInfo* connectionInfo,
                                   const ParsedUrlInfo& parsedUrl,
                                   const QString& postData);

    /**
     * @brief 处理请求重试逻辑
     * @param parsedUrl 解析后的URL信息
     * @param httpRequest HTTP请求字符串
     * @return HTTP响应内容，失败时返回空字符串
     */
    QString handleRequestRetry(const ParsedUrlInfo& parsedUrl,
                             const QString& httpRequest);

    /**
     * @brief 更新请求性能统计
     * @param startTime 请求开始时间
     * @param success 请求是否成功
     * @param responseLength 响应长度
     */
    void updateRequestPerformanceStats(const std::chrono::high_resolution_clock::time_point& startTime,
                                     bool success,
                                     int responseLength);

    // ========== HTTP通信方法 ==========

    /**
     * @brief 发送HTTP请求到TLS连接
     * @param connectionInfo TLS连接信息
     * @param httpRequest HTTP请求字符串
     * @return 发送是否成功
     */
    bool sendHttpRequest(ConnectionInfo* connectionInfo, const QString& httpRequest);

    /**
     * @brief 从TLS连接接收HTTP响应
     * @param connectionInfo TLS连接信息
     * @return HTTP响应字符串，失败时返回空字符串
     */
    QString receiveHttpResponse(ConnectionInfo* connectionInfo);

    /**
     * @brief 解析HTTP响应，提取响应体
     * @param response 完整的HTTP响应
     * @return HTTP响应体，失败时返回空字符串
     */
    QString parseHttpResponse(const QString& response, ConnectionInfo* connectionInfo = nullptr);

    /**
     * @brief 提取HTTP头中的Content-Length值
     * @param headers HTTP头字符串
     * @return Content-Length值，未找到时返回-1
     */
    int extractContentLength(const QString& headers);

    /**
     * @brief 解码chunked传输编码的响应
     * @param chunkedData chunked编码的数据
     * @return 解码后的数据
     */
    QString decodeChunkedResponse(const QString& chunkedData);

    /**
     * @brief 解压gzip压缩的响应
     * @param gzipData gzip压缩的数据
     * @return 解压后的数据
     */
    QString decompressGzipResponse(const QString& gzipData);

    // ========== 现代C++工具函数 ==========

    /**
     * @brief 安全的指针检查和操作
     * @tparam T 指针类型
     * @tparam Func 操作函数类型
     * @param ptr 要检查的指针
     * @param operation 要执行的操作
     * @return 操作结果，指针为空时返回默认值
     */
    template<typename T, typename Func>
    auto safePointerOperation(T* ptr, Func&& operation) -> decltype(operation(ptr)) {
        return ptr ? operation(ptr) : decltype(operation(ptr)){};
    }

    /**
     * @brief 条件执行辅助函数
     * @tparam Func 函数类型
     * @param condition 条件
     * @param operation 要执行的操作
     */
    template<typename Func>
    void executeIf(bool condition, Func&& operation) {
        if (condition) {
            operation();
        }
    }

    /**
     * @brief 获取HTTP方法字符串
     * @param hasPostData 是否有POST数据
     * @return HTTP方法字符串
     */
    constexpr const char* getHttpMethod(bool hasPostData) noexcept {
        return hasPostData ? "POST" : "GET";
    }



private:
#ifdef OPENSSL_FOUND
    // OpenSSL上下文
    SSL_CTX* m_sslContext = nullptr;
#else
    // Windows SChannel上下文
    SCHANNEL_CRED m_schannelCred;
    bool m_schannelInitialized = false;
#endif

    // 连接池
    std::vector<std::unique_ptr<ConnectionInfo>> m_connectionPool;
    mutable QMutex m_poolMutex;
    int m_poolSize = 5;

    // 定时器
    QTimer* m_keepAliveTimer = nullptr;
    QTimer* m_cleanupTimer = nullptr;

    // 性能统计
    mutable PerformanceStats m_stats;
    mutable QMutex m_statsMutex;

    // 初始化状态
    bool m_initialized = false;

    // Windows Socket初始化
    bool m_wsaInitialized = false;

    // 浏览器指纹类型
    BrowserFingerprint m_browserType = BrowserFingerprint::QUARK_BROWSER;

    // 调试监控器
    UltraFastTLSDebugMonitor* m_debugMonitor = nullptr;

    // 安静模式（类似curl的行为）
    bool m_quietMode = false;

};

#endif // ULTRAFASTTLS_H
