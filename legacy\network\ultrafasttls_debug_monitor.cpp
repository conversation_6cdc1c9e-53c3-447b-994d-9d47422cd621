#include "ultrafasttls_debug_monitor.h"
#include <QUuid>
#include <QDir>
#include <QStandardPaths>

UltraFastTLSDebugMonitor::UltraFastTLSDebugMonitor(QObject *parent)
    : QObject(parent)
    , m_timeoutTimer(new QTimer(this))
    , m_flushTimer(new QTimer(this))
    , m_logToFile(false)
    , m_logFile(nullptr)
    , m_logStream(nullptr)
    , m_maxOperationHistory(100)
    , m_debugLevel(1)
    , m_timeoutCheckInterval(1000)
    , m_totalOperations(0)
    , m_successfulOperations(0)
    , m_failedOperations(0)
    , m_timeoutOperations(0)
    , m_totalExecutionTime(0)
{
    // 设置超时检查定时器
    m_timeoutTimer->setInterval(m_timeoutCheckInterval);
    connect(m_timeoutTimer, &QTimer::timeout, this, &UltraFastTLSDebugMonitor::checkForTimeouts);
    m_timeoutTimer->start();
    
    // 设置日志刷新定时器
    m_flushTimer->setInterval(5000); // 每5秒刷新一次日志
    connect(m_flushTimer, &QTimer::timeout, this, &UltraFastTLSDebugMonitor::flushLogFile);
    
    // 默认启用文件日志
    QString defaultLogPath = QStandardPaths::writableLocation(QStandardPaths::TempLocation) 
                           + "/ultrafasttls_debug.log";
    setLogToFile(true, defaultLogPath);
    
    writeToLog("=== UltraFastTLS调试监控器启动 ===");
}

UltraFastTLSDebugMonitor::~UltraFastTLSDebugMonitor()
{
    writeToLog("=== UltraFastTLS调试监控器关闭 ===");
    
    if (m_logStream) {
        delete m_logStream;
    }
    if (m_logFile) {
        m_logFile->close();
        delete m_logFile;
    }
}

QString UltraFastTLSDebugMonitor::startOperation(const QString& url, int timeoutMs)
{
    QMutexLocker locker(&m_mutex);
    
    QString operationId = QUuid::createUuid().toString(QUuid::WithoutBraces);
    
    OperationInfo info;
    info.operationId = operationId;
    info.state = OperationState::INITIALIZING;
    info.startTime = QDateTime::currentDateTime();
    info.lastUpdateTime = info.startTime;
    info.url = url;
    info.timeoutMs = timeoutMs;
    info.currentStep = "开始操作";
    
    m_operations[operationId] = info;
    m_totalOperations++;
    
    QString message = QString("[操作开始] ID: %1, URL: %2, 超时: %3ms")
                     .arg(operationId.left(8))
                     .arg(url)
                     .arg(timeoutMs);
    writeToLog(message);
    
    emit operationStarted(operationId, url);
    emit debugMessage(message);
    
    return operationId;
}

void UltraFastTLSDebugMonitor::updateOperationState(const QString& operationId, OperationState state, const QString& step)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_operations.contains(operationId)) {
        return;
    }
    
    OperationInfo& info = m_operations[operationId];
    info.state = state;
    info.lastUpdateTime = QDateTime::currentDateTime();
    if (!step.isEmpty()) {
        info.currentStep = step;
    }
    
    QString message = QString("[状态更新] ID: %1, 状态: %2, 步骤: %3")
                     .arg(operationId.left(8))
                     .arg(stateToString(state))
                     .arg(step.isEmpty() ? info.currentStep : step);
    writeToLog(message);
    
    emit operationStateChanged(operationId, static_cast<int>(state), step);
    emit debugMessage(message);
}

void UltraFastTLSDebugMonitor::addOperationMessage(const QString& operationId, const QString& message)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_operations.contains(operationId)) {
        return;
    }
    
    OperationInfo& info = m_operations[operationId];
    info.debugMessages.append(QString("[%1] %2")
                             .arg(QDateTime::currentDateTime().toString("hh:mm:ss.zzz"))
                             .arg(message));
    info.lastUpdateTime = QDateTime::currentDateTime();
    
    QString logMessage = QString("[操作消息] ID: %1, %2")
                        .arg(operationId.left(8))
                        .arg(message);
    writeToLog(logMessage);
    emit debugMessage(logMessage);
}

void UltraFastTLSDebugMonitor::completeOperation(const QString& operationId, bool success, const QString& result)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_operations.contains(operationId)) {
        return;
    }
    
    OperationInfo& info = m_operations[operationId];
    info.state = success ? OperationState::COMPLETED : OperationState::FAILED;
    info.lastUpdateTime = QDateTime::currentDateTime();
    
    qint64 executionTime = info.startTime.msecsTo(info.lastUpdateTime);
    m_totalExecutionTime += executionTime;
    
    if (success) {
        m_successfulOperations++;
        info.currentStep = "操作成功完成";
    } else {
        m_failedOperations++;
        info.currentStep = "操作失败";
    }
    
    QString message = QString("[操作完成] ID: %1, 成功: %2, 耗时: %3ms, 结果: %4")
                     .arg(operationId.left(8))
                     .arg(success ? "是" : "否")
                     .arg(executionTime)
                     .arg(result.left(50));
    writeToLog(message);
    
    emit operationCompleted(operationId, success);
    emit debugMessage(message);
    
    // 清理旧操作
    cleanupOldOperations();
}

void UltraFastTLSDebugMonitor::checkForTimeouts()
{
    QMutexLocker locker(&m_mutex);
    
    QDateTime currentTime = QDateTime::currentDateTime();
    QStringList timeoutOperations;
    
    for (auto it = m_operations.begin(); it != m_operations.end(); ++it) {
        const OperationInfo& info = it.value();
        
        if (info.state != OperationState::COMPLETED &&
            info.state != OperationState::FAILED &&
            info.state != OperationState::TIMEOUT) {
            
            qint64 elapsed = info.startTime.msecsTo(currentTime);
            if (elapsed > info.timeoutMs) {
                timeoutOperations.append(it.key());
            }
        }
    }
    
    // 处理超时操作
    for (const QString& operationId : timeoutOperations) {
        OperationInfo& info = m_operations[operationId];
        info.state = OperationState::TIMEOUT;
        info.lastUpdateTime = currentTime;
        info.currentStep = "操作超时";
        
        m_timeoutOperations++;
        
        QString message = QString("[操作超时] ID: %1, URL: %2, 耗时: %3ms")
                         .arg(operationId.left(8))
                         .arg(info.url)
                         .arg(info.startTime.msecsTo(currentTime));
        writeToLog(message);
        
        emit operationTimeout(operationId, info.url);
        emit debugMessage(message);
    }
}

QString UltraFastTLSDebugMonitor::generateDebugReport() const
{
    QMutexLocker locker(&m_mutex);
    
    QString report;
    report += "=== UltraFastTLS调试报告 ===\n";
    report += QString("生成时间: %1\n").arg(QDateTime::currentDateTime().toString());
    report += QString("总操作数: %1\n").arg(m_totalOperations);
    report += QString("成功操作: %1\n").arg(m_successfulOperations);
    report += QString("失败操作: %1\n").arg(m_failedOperations);
    report += QString("超时操作: %1\n").arg(m_timeoutOperations);
    
    if (m_totalOperations > 0) {
        double successRate = (double)m_successfulOperations / m_totalOperations * 100;
        double avgTime = (double)m_totalExecutionTime / m_totalOperations;
        report += QString("成功率: %.1f%%\n").arg(successRate);
        report += QString("平均耗时: %.1fms\n").arg(avgTime);
    }
    
    report += "\n=== 当前活跃操作 ===\n";
    QDateTime currentTime = QDateTime::currentDateTime();
    
    for (auto it = m_operations.constBegin(); it != m_operations.constEnd(); ++it) {
        const OperationInfo& info = it.value();
        
        if (info.state != OperationState::COMPLETED &&
            info.state != OperationState::FAILED) {
            
            qint64 elapsed = info.startTime.msecsTo(currentTime);
            report += QString("ID: %1, 状态: %2, 耗时: %3ms, URL: %4\n")
                     .arg(info.operationId.left(8))
                     .arg(stateToString(info.state))
                     .arg(elapsed)
                     .arg(info.url);
            
            if (!info.debugMessages.isEmpty()) {
                report += "  最近消息:\n";
                int messageCount = qMin(3, info.debugMessages.size());
                for (int i = info.debugMessages.size() - messageCount; i < info.debugMessages.size(); ++i) {
                    report += QString("    %1\n").arg(info.debugMessages[i]);
                }
            }
        }
    }
    
    return report;
}

void UltraFastTLSDebugMonitor::setLogToFile(bool enabled, const QString& filePath)
{
    QMutexLocker locker(&m_mutex);
    
    // 关闭现有日志文件
    if (m_logStream) {
        delete m_logStream;
        m_logStream = nullptr;
    }
    if (m_logFile) {
        m_logFile->close();
        delete m_logFile;
        m_logFile = nullptr;
    }
    
    m_logToFile = enabled;
    
    if (enabled && !filePath.isEmpty()) {
        m_logFilePath = filePath;
        m_logFile = new QFile(m_logFilePath);
        
        if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
            m_logStream = new QTextStream(m_logFile);
            m_flushTimer->start();
            
            writeToLog(QString("日志文件已启用: %1").arg(m_logFilePath));
        } else {
            delete m_logFile;
            m_logFile = nullptr;
            m_logToFile = false;
        }
    }
}

void UltraFastTLSDebugMonitor::writeToLog(const QString& message)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString logLine = QString("[%1] %2").arg(timestamp, message);
    
    // 输出到控制台
    qDebug() << logLine;
    
    // 输出到文件
    if (m_logToFile && m_logStream) {
        *m_logStream << logLine << Qt::endl;
    }
}

QString UltraFastTLSDebugMonitor::stateToString(OperationState state) const
{
    switch (state) {
        case OperationState::IDLE: return "空闲";
        case OperationState::INITIALIZING: return "初始化";
        case OperationState::CREATING_CONNECTION: return "创建连接";
        case OperationState::TLS_HANDSHAKE: return "TLS握手";
        case OperationState::SENDING_REQUEST: return "发送请求";
        case OperationState::WAITING_RESPONSE: return "等待响应";
        case OperationState::PROCESSING_RESPONSE: return "处理响应";
        case OperationState::COMPLETED: return "已完成";
        case OperationState::FAILED: return "错误";
        case OperationState::TIMEOUT: return "超时";
        default: return "未知";
    }
}

void UltraFastTLSDebugMonitor::cleanupOldOperations()
{
    if (m_operations.size() <= m_maxOperationHistory) {
        return;
    }
    
    // 保留最近的操作，删除旧的已完成操作
    QList<QString> completedOperations;
    for (auto it = m_operations.constBegin(); it != m_operations.constEnd(); ++it) {
        const OperationInfo& info = it.value();
        if (info.state == OperationState::COMPLETED ||
            info.state == OperationState::FAILED ||
            info.state == OperationState::TIMEOUT) {
            completedOperations.append(it.key());
        }
    }
    
    // 按时间排序，删除最旧的
    std::sort(completedOperations.begin(), completedOperations.end(), 
              [this](const QString& a, const QString& b) {
                  return m_operations[a].lastUpdateTime < m_operations[b].lastUpdateTime;
              });
    
    int toRemove = m_operations.size() - m_maxOperationHistory;
    for (int i = 0; i < toRemove && i < completedOperations.size(); ++i) {
        m_operations.remove(completedOperations[i]);
    }
}

void UltraFastTLSDebugMonitor::flushLogFile()
{
    if (m_logStream) {
        m_logStream->flush();
    }
    if (m_logFile) {
        m_logFile->flush();
    }
}
