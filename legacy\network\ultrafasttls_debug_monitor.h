#ifndef ULTRAFASTTLS_DEBUG_MONITOR_H
#define ULTRAFASTTLS_DEBUG_MONITOR_H

#include <QObject>
#include <QTimer>
#include <QMutex>
#include <QDateTime>
#include <QDebug>
#include <QFile>
#include <QTextStream>
#include <chrono>

/**
 * @brief UltraFastTLS调试监控器
 * 
 * 专门用于监控和调试UltraFastTLS在登录时的卡死问题
 * 提供详细的状态跟踪、性能监控和问题诊断功能
 */
class UltraFastTLSDebugMonitor : public QObject
{
    Q_OBJECT

public:
    enum class OperationState {
        IDLE,                    // 空闲状态
        INITIALIZING,           // 初始化中
        CREATING_CONNECTION,    // 创建连接中
        TLS_HANDSHAKE,         // TLS握手中
        SENDING_REQUEST,       // 发送请求中
        WAITING_RESPONSE,      // 等待响应中
        PROCESSING_RESPONSE,   // 处理响应中
        COMPLETED,             // 完成
        FAILED,                // 错误状态（避免与Windows ERROR宏冲突）
        TIMEOUT                // 超时状态
    };

    struct OperationInfo {
        QString operationId;
        OperationState state;
        QDateTime startTime;
        QDateTime lastUpdateTime;
        QString currentStep;
        QString url;
        int timeoutMs;
        QStringList debugMessages;
        
        OperationInfo() : state(OperationState::IDLE), timeoutMs(30000) {}
    };

    explicit UltraFastTLSDebugMonitor(QObject *parent = nullptr);
    ~UltraFastTLSDebugMonitor();

    // 操作跟踪
    QString startOperation(const QString& url, int timeoutMs = 30000);
    void updateOperationState(const QString& operationId, OperationState state, const QString& step = "");
    void addOperationMessage(const QString& operationId, const QString& message);
    void completeOperation(const QString& operationId, bool success, const QString& result = "");

    // 状态查询
    OperationInfo getOperationInfo(const QString& operationId) const;
    QStringList getActiveOperations() const;
    bool isOperationActive(const QString& operationId) const;
    bool hasTimeoutOperations() const;

    // 调试报告
    QString generateDebugReport() const;
    QString generateTimeoutReport() const;
    QString generatePerformanceReport() const;

    // 配置
    void setLogToFile(bool enabled, const QString& filePath = "");
    void setMaxOperationHistory(int maxCount) { m_maxOperationHistory = maxCount; }
    void setDebugLevel(int level) { m_debugLevel = level; }

signals:
    void operationStarted(const QString& operationId, const QString& url);
    void operationStateChanged(const QString& operationId, int state, const QString& step);
    void operationCompleted(const QString& operationId, bool success);
    void operationTimeout(const QString& operationId, const QString& url);
    void debugMessage(const QString& message);

private slots:
    void checkForTimeouts();
    void flushLogFile();

private:
    void writeToLog(const QString& message);
    QString stateToString(OperationState state) const;
    void cleanupOldOperations();

    mutable QMutex m_mutex;
    QHash<QString, OperationInfo> m_operations;
    QTimer* m_timeoutTimer;
    QTimer* m_flushTimer;
    
    // 日志配置
    bool m_logToFile;
    QString m_logFilePath;
    QFile* m_logFile;
    QTextStream* m_logStream;
    
    // 配置参数
    int m_maxOperationHistory;
    int m_debugLevel;
    int m_timeoutCheckInterval;
    
    // 统计信息
    int m_totalOperations;
    int m_successfulOperations;
    int m_failedOperations;
    int m_timeoutOperations;
    qint64 m_totalExecutionTime;
};

/**
 * @brief RAII操作监控器
 * 
 * 自动管理操作的开始和结束，确保操作状态正确更新
 */
class OperationMonitor
{
public:
    explicit OperationMonitor(UltraFastTLSDebugMonitor* monitor, const QString& url, int timeoutMs = 30000)
        : m_monitor(monitor), m_success(false)
    {
        if (m_monitor) {
            m_operationId = m_monitor->startOperation(url, timeoutMs);
        }
    }
    
    ~OperationMonitor()
    {
        if (m_monitor && !m_operationId.isEmpty()) {
            m_monitor->completeOperation(m_operationId, m_success, m_result);
        }
    }
    
    void updateState(UltraFastTLSDebugMonitor::OperationState state, const QString& step = "")
    {
        if (m_monitor && !m_operationId.isEmpty()) {
            m_monitor->updateOperationState(m_operationId, state, step);
        }
    }
    
    void addMessage(const QString& message)
    {
        if (m_monitor && !m_operationId.isEmpty()) {
            m_monitor->addOperationMessage(m_operationId, message);
        }
    }
    
    void setSuccess(bool success, const QString& result = "")
    {
        m_success = success;
        m_result = result;
    }
    
    QString operationId() const { return m_operationId; }

private:
    UltraFastTLSDebugMonitor* m_monitor;
    QString m_operationId;
    bool m_success;
    QString m_result;
};

// 便利宏定义
#define DEBUG_MONITOR_OPERATION(monitor, url) \
    OperationMonitor _op_monitor(monitor, url)

#define DEBUG_MONITOR_STATE(state, step) \
    _op_monitor.updateState(UltraFastTLSDebugMonitor::OperationState::state, step)

#define DEBUG_MONITOR_MESSAGE(message) \
    _op_monitor.addMessage(message)

#define DEBUG_MONITOR_SUCCESS(success, result) \
    _op_monitor.setSuccess(success, result)

#endif // ULTRAFASTTLS_DEBUG_MONITOR_H
