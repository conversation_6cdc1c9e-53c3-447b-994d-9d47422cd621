#ifndef SERVICE_CONTAINER_H
#define SERVICE_CONTAINER_H

#include <QObject>
#include <memory>

// 前向声明
class NetworkClient;
class EncryptionService;
class JsonParser;
class AuthenticationService;

/**
 * @brief 服务容器 - 管理所有服务的生命周期和依赖注入
 * 
 * 单一责任：只负责服务的创建、配置和依赖注入
 */
class ServiceContainer : public QObject
{
    Q_OBJECT

public:
    explicit ServiceContainer(QObject* parent = nullptr);
    ~ServiceContainer();
    
    // 初始化所有服务
    bool initialize();
    
    // 获取服务实例
    NetworkClient* getNetworkClient() const { return m_networkClient.get(); }
    EncryptionService* getEncryptionService() const { return m_encryptionService.get(); }
    JsonParser* getJsonParser() const { return m_jsonParser.get(); }
    AuthenticationService* getAuthenticationService() const { return m_authenticationService.get(); }
    
    // 配置方法
    void setCurlPath(const QString& path);
    void setNetworkTimeout(int timeoutMs);
    void setEncryptionKey(const QString& key);

signals:
    void serviceInitialized(const QString& serviceName);
    void initializationCompleted(bool success);

private:
    // 服务实例
    std::unique_ptr<NetworkClient> m_networkClient;
    std::unique_ptr<EncryptionService> m_encryptionService;
    std::unique_ptr<JsonParser> m_jsonParser;
    std::unique_ptr<AuthenticationService> m_authenticationService;
    
    bool m_initialized = false;
    
    // 初始化方法
    bool initializeNetworkClient();
    bool initializeEncryptionService();
    bool initializeJsonParser();
    bool initializeAuthenticationService();
    void setupDependencies();
    void connectSignals();
};

#endif // SERVICE_CONTAINER_H
