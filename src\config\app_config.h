#ifndef APP_CONFIG_H
#define APP_CONFIG_H

#include <QString>
#include <QSettings>
#include <QJsonObject>

/**
 * @brief 应用程序配置管理
 */
class AppConfig {
public:
    // 单例模式
    static AppConfig& instance();
    
    // 网络配置
    struct NetworkConfig {
        QString engineType = "UltraFastTLS";
        int timeout = 30000;
        int refreshInterval = 1800;
        bool enableDebugLog = false;
        QString userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
    };
    
    // UI配置
    struct UIConfig {
        int pageSize = 50;
        bool focusOnly = false;
        QString gameId = "110";
        bool enableInterval = true;
        int windowWidth = 1200;
        int windowHeight = 800;
    };
    
    // 过滤配置
    struct FilterConfig {
        QString includeKeywords;
        QString excludeKeywords;
        double minPrice = 0.0;
        double maxPrice = 999999.0;
        bool enableAutoAccept = false;
    };
    
    // 安全配置
    struct SecurityConfig {
        bool enableTLSFingerprint = true;
        QString fingerprintType = "Quark";
        bool enableRandomDelay = false;
        int minDelay = 100;
        int maxDelay = 500;
    };
    
    // 配置访问
    NetworkConfig& network() { return m_networkConfig; }
    UIConfig& ui() { return m_uiConfig; }
    FilterConfig& filter() { return m_filterConfig; }
    SecurityConfig& security() { return m_securityConfig; }
    
    // 配置持久化
    void load();
    void save();
    void reset();
    
    // 配置验证
    bool validate() const;
    QStringList getValidationErrors() const;
    
    // 配置导入导出
    bool exportToFile(const QString& filePath) const;
    bool importFromFile(const QString& filePath);
    QJsonObject toJson() const;
    bool fromJson(const QJsonObject& json);

private:
    AppConfig() = default;
    ~AppConfig() = default;
    AppConfig(const AppConfig&) = delete;
    AppConfig& operator=(const AppConfig&) = delete;
    
    NetworkConfig m_networkConfig;
    UIConfig m_uiConfig;
    FilterConfig m_filterConfig;
    SecurityConfig m_securityConfig;
    
    QSettings* m_settings = nullptr;
    
    void initializeDefaults();
    void loadFromSettings();
    void saveToSettings();
};

// 便捷宏定义
#define CONFIG AppConfig::instance()
#define NETWORK_CONFIG CONFIG.network()
#define UI_CONFIG CONFIG.ui()
#define FILTER_CONFIG CONFIG.filter()
#define SECURITY_CONFIG CONFIG.security()

#endif // APP_CONFIG_H
