#include "order_service.h"
#include "../../network/network_manager.h"
#include "../utils/logger.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QTimer>

// OrderServiceConfig 实现
bool OrderServiceConfig::isValid() const
{
    return refreshInterval >= 100 && 
           refreshInterval <= 60000 &&
           maxRetryCount >= 0 &&
           maxRetryCount <= 10 &&
           requestTimeout >= 1000 &&
           requestTimeout <= 300000 &&
           pageSize >= 1 &&
           pageSize <= 200;
}

// OrderService 实现
OrderService::OrderService(QObject* parent)
    : QObject(parent)
    , m_refreshTimer(new QTimer(this))
    , m_currentAccountIndex(-1)
{
    // 初始化性能统计
    m_performanceStats.startTime = QDateTime::currentDateTime();
    
    // 连接定时器
    connect(m_refreshTimer, &QTimer::timeout, this, &OrderService::onRefreshTimer);
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "OrderService initialized");
}

OrderService::~OrderService()
{
    stopAutoRefresh();
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "OrderService destroyed");
}

void OrderService::setConfig(const OrderServiceConfig& config)
{
    if (!config.isValid()) {
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, "Invalid OrderService configuration");
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    m_config = config;
    
    // 更新定时器间隔
    if (m_refreshTimer->isActive()) {
        m_refreshTimer->setInterval(m_config.refreshInterval);
    }
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, QString("OrderService configuration updated: interval=%1ms")
                 .arg(m_config.refreshInterval));
}

OrderServiceConfig OrderService::getConfig() const
{
    QMutexLocker locker(&m_mutex);
    return m_config;
}

void OrderService::setNetworkManager(NetworkManager* networkManager)
{
    if (m_networkManager) {
        // 断开旧连接
        disconnect(m_networkManager, nullptr, this, nullptr);
    }
    
    m_networkManager = networkManager;
    
    if (m_networkManager) {
        // 连接新的网络管理器信号
        connect(m_networkManager, &NetworkManager::requestCompleted,
                this, &OrderService::onNetworkRequestCompleted);
        connect(m_networkManager, &NetworkManager::requestFailed,
                this, &OrderService::onNetworkRequestFailed);
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, "NetworkManager connected to OrderService");
    }
}

NetworkManager* OrderService::getNetworkManager() const
{
    return m_networkManager;
}

void OrderService::setMainAccount(const MainAccountInfo& account)
{
    QMutexLocker locker(&m_mutex);
    m_mainAccount = account;
    
    NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Main account set: %1")
                 .arg(account.username));
}

void OrderService::addSubAccount(const AccountInfo& account)
{
    QMutexLocker locker(&m_mutex);
    
    // 检查是否已存在
    for (int i = 0; i < m_subAccounts.size(); ++i) {
        if (m_subAccounts[i].username == account.username) {
            m_subAccounts[i] = account;
            NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Sub account updated: %1")
                         .arg(account.username));
            return;
        }
    }
    
    // 添加新账号
    m_subAccounts.append(account);
    NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Sub account added: %1")
                 .arg(account.username));
}

void OrderService::removeSubAccount(const QString& username)
{
    QMutexLocker locker(&m_mutex);
    
    for (int i = 0; i < m_subAccounts.size(); ++i) {
        if (m_subAccounts[i].username == username) {
            m_subAccounts.removeAt(i);
            NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Sub account removed: %1")
                         .arg(username));
            return;
        }
    }
}

void OrderService::clearSubAccounts()
{
    QMutexLocker locker(&m_mutex);
    int count = m_subAccounts.size();
    m_subAccounts.clear();
    
    NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Cleared %1 sub accounts").arg(count));
}

QList<AccountInfo> OrderService::getLoggedInAccounts() const
{
    QMutexLocker locker(&m_mutex);
    QList<AccountInfo> loggedIn;
    
    for (const AccountInfo& account : m_subAccounts) {
        if (account.isLoggedIn) {
            loggedIn.append(account);
        }
    }
    
    return loggedIn;
}

void OrderService::startAutoRefresh()
{
    if (!m_networkManager) {
        emit errorOccurred("NetworkManager not set");
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    if (m_refreshTimer->isActive()) {
        NEW_LOG_WARNING(NewLogCategory::ORDER, "Auto refresh already running");
        return;
    }
    
    m_refreshTimer->setInterval(m_config.refreshInterval);
    m_refreshTimer->start();
    m_currentAccountIndex = -1; // 从主账号开始
    
    emit refreshStarted();
    NEW_LOG_INFO(NewLogCategory::ORDER, "Auto refresh started");
}

void OrderService::stopAutoRefresh()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_refreshTimer->isActive()) {
        m_refreshTimer->stop();
        m_isRefreshing = false;
        m_currentAccountIndex = -1;
        m_currentRefreshingAccount.clear();
        
        NEW_LOG_INFO(NewLogCategory::ORDER, "Auto refresh stopped");
    }
}

bool OrderService::isAutoRefreshing() const
{
    return m_refreshTimer->isActive();
}

void OrderService::refreshOrders()
{
    if (!m_networkManager) {
        emit errorOccurred("NetworkManager not set");
        return;
    }
    
    // 开始新一轮刷新
    m_currentAccountIndex = -1;
    refreshNextAccount();
}

void OrderService::refreshOrdersForAccount(const QString& accountId)
{
    if (!m_networkManager) {
        emit errorOccurred("NetworkManager not set");
        return;
    }
    
    // 构建请求参数
    QJsonObject headers;
    headers["Content-Type"] = "application/x-www-form-urlencoded";
    headers["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
    
    QString postData = QString("Action=LevelOrderList&GameID=%1&PageSize=%2")
                      .arg(m_config.gameId)
                      .arg(m_config.pageSize);
    
    // 添加账号特定的认证信息
    if (accountId == "main" && m_mainAccount.isLoggedIn) {
        postData += QString("&Token=%1&UserID=%2")
                   .arg(m_mainAccount.token)
                   .arg(m_mainAccount.userId);
    } else {
        // 查找子账号
        for (const AccountInfo& account : m_subAccounts) {
            if (account.username == accountId && account.isLoggedIn) {
                postData += QString("&Token=%1&UserID=%2")
                           .arg(account.token)
                           .arg(account.userId);
                break;
            }
        }
    }
    
    // 发送异步请求
    m_networkManager->executeRequestAsync(
        "https://server.dailiantong.com.cn/API/AppService.ashx",
        postData,
        headers
    );
    
    m_currentRefreshingAccount = accountId;
    emit accountRefreshStarted(accountId);
    
    NEW_LOG_INFO(NewLogCategory::ORDER, QString("Refreshing orders for account: %1")
                 .arg(accountId));
}

QList<OrderInfo> OrderService::getOrders() const
{
    return m_orderManager.getAllOrders();
}

QList<OrderInfo> OrderService::getFilteredOrders(const OrderFilter& filter) const
{
    return m_orderManager.getFilteredOrders(filter);
}

OrderStats OrderService::getOrderStats() const
{
    return m_orderManager.getStats();
}

void OrderService::setOrderFilter(const OrderFilter& filter)
{
    QMutexLocker locker(&m_mutex);
    m_orderFilter = filter;
    
    NEW_LOG_INFO(NewLogCategory::ORDER, "Order filter updated");
}

OrderFilter OrderService::getOrderFilter() const
{
    QMutexLocker locker(&m_mutex);
    return m_orderFilter;
}

bool OrderService::isRefreshing() const
{
    return m_isRefreshing;
}

QString OrderService::getCurrentRefreshingAccount() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentRefreshingAccount;
}

int OrderService::getRefreshProgress() const
{
    QMutexLocker locker(&m_mutex);
    
    int totalAccounts = 1; // 主账号
    if (m_mainAccount.isLoggedIn) {
        totalAccounts += getLoggedInAccounts().size();
    }
    
    if (totalAccounts == 0) return 0;
    
    int currentProgress = m_currentAccountIndex + 1;
    return (currentProgress * 100) / totalAccounts;
}

OrderService::PerformanceStats OrderService::getPerformanceStats() const
{
    QMutexLocker locker(&m_mutex);
    return m_performanceStats;
}

void OrderService::resetPerformanceStats()
{
    QMutexLocker locker(&m_mutex);
    m_performanceStats = PerformanceStats();
    m_performanceStats.lastRefreshTime = QDateTime::currentDateTime();

    NEW_LOG_INFO(NewLogCategory::SYSTEM, "Performance stats reset");
}

// 私有方法实现
void OrderService::onRefreshTimer()
{
    if (m_isRefreshing) {
        NEW_LOG_WARNING(NewLogCategory::ORDER, "Previous refresh still in progress, skipping");
        return;
    }

    refreshNextAccount();
}

void OrderService::refreshNextAccount()
{
    QMutexLocker locker(&m_mutex);

    m_isRefreshing = true;

    // 确定要刷新的账号
    QString accountToRefresh;

    if (m_currentAccountIndex == -1) {
        // 刷新主账号
        if (m_mainAccount.isLoggedIn) {
            accountToRefresh = "main";
            m_currentAccountIndex = 0;
        } else {
            // 主账号未登录，跳到子账号
            m_currentAccountIndex = 0;
        }
    }

    // 如果主账号已处理或未登录，处理子账号
    if (accountToRefresh.isEmpty() && m_currentAccountIndex >= 0) {
        QList<AccountInfo> loggedInAccounts = getLoggedInAccounts();

        if (m_currentAccountIndex < loggedInAccounts.size()) {
            accountToRefresh = loggedInAccounts[m_currentAccountIndex].username;
            m_currentAccountIndex++;
        } else {
            // 所有账号都已刷新完毕
            m_isRefreshing = false;
            m_currentAccountIndex = -1;
            m_currentRefreshingAccount.clear();

            emit refreshCompleted(true, "All accounts refreshed");
            NEW_LOG_INFO(NewLogCategory::ORDER, "Refresh cycle completed");
            return;
        }
    }

    if (!accountToRefresh.isEmpty()) {
        refreshOrdersForAccount(accountToRefresh);
    } else {
        // 没有可刷新的账号
        m_isRefreshing = false;
        m_currentAccountIndex = -1;
        emit refreshCompleted(false, "No logged in accounts");
        NEW_LOG_WARNING(NewLogCategory::ORDER, "No logged in accounts to refresh");
    }
}

void OrderService::onNetworkRequestCompleted(const QString& engineName, const NetworkResult& result)
{
    Q_UNUSED(engineName)

    if (!result.success) {
        onNetworkRequestFailed(engineName, result.errorMessage);
        return;
    }

    // 处理订单响应
    processOrderResponse(result.data, m_currentRefreshingAccount);

    // 更新性能统计
    updatePerformanceStats(true, result.responseTime);

    // 发送账号刷新完成信号
    emit accountRefreshCompleted(m_currentRefreshingAccount, true);

    // 继续刷新下一个账号
    QTimer::singleShot(100, this, &OrderService::refreshNextAccount);
}

void OrderService::onNetworkRequestFailed(const QString& engineName, const QString& error)
{
    NEW_LOG_ERROR(NewLogCategory::NETWORK,
                  QString("Network request failed: engine=%1, error=%2")
                  .arg(engineName).arg(error));

    // 更新性能统计
    updatePerformanceStats(false, 0);

    // 发送账号刷新失败信号
    emit accountRefreshCompleted(m_currentRefreshingAccount, false);

    // 继续刷新下一个账号（即使当前失败）
    QTimer::singleShot(1000, this, &OrderService::refreshNextAccount);
}

void OrderService::processOrderResponse(const QString& response, const QString& accountId)
{
    try {
        // 解析JSON响应
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &parseError);

        if (parseError.error != QJsonParseError::NoError) {
            NEW_LOG_ERROR(NewLogCategory::ORDER,
                          QString("Failed to parse order response: %1").arg(parseError.errorString()));
            return;
        }

        QJsonObject rootObj = doc.object();
        QJsonArray orderArray = rootObj["LevelOrderList"].toArray();

        QList<OrderInfo> newOrders;

        // 解析每个订单
        for (const QJsonValue& value : orderArray) {
            QJsonObject orderObj = value.toObject();
            OrderInfo order = OrderInfo::fromJson(orderObj);

            if (order.isValid()) {
                newOrders.append(order);
            }
        }

        // 更新订单管理器
        for (const OrderInfo& order : newOrders) {
            m_orderManager.addOrder(order);
        }

        // 更新统计
        updateOrderStats();

        // 发送订单更新信号
        emit ordersUpdated(m_orderManager.getAllOrders());

        NEW_LOG_INFO(NewLogCategory::ORDER,
                     QString("Processed %1 orders for account %2")
                     .arg(newOrders.size()).arg(accountId));

    } catch (const std::exception& e) {
        NEW_LOG_ERROR(NewLogCategory::ORDER,
                      QString("Exception processing order response: %1").arg(e.what()));
    }
}

void OrderService::updateOrderStats()
{
    OrderStats stats = m_orderManager.getStats();
    emit statsUpdated(stats);
}

void OrderService::updatePerformanceStats(bool success, qint64 responseTime)
{
    QMutexLocker locker(&m_mutex);

    m_performanceStats.totalRefreshes++;

    if (success) {
        m_performanceStats.successfulRefreshes++;
    } else {
        m_performanceStats.failedRefreshes++;
    }

    m_performanceStats.totalRefreshTime += responseTime;
    m_performanceStats.averageRefreshTime = m_performanceStats.totalRefreshes > 0
        ? m_performanceStats.totalRefreshTime / m_performanceStats.totalRefreshes
        : 0;

    m_performanceStats.lastRefreshTime = QDateTime::currentDateTime();

    emit performanceStatsUpdated(m_performanceStats);
}
