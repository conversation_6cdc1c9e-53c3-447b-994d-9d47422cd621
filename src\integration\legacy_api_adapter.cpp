#include "legacy_api_adapter.h"
#include <orderapi.h>
#include <logger.h>
#include "../config/app_config.h"
#include <QDebug>

LegacyApiAdapter::LegacyApiAdapter(QObject *parent)
    : QObject(parent)
    , m_legacy<PERSON>pi(nullptr)
    , m_networkEngine(nullptr)
    , m_useNewEngine(false)
    , m_initialized(false)
{
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "LegacyApiAdapter 创建");
}

LegacyApiAdapter::~LegacyApiAdapter()
{
    if (m_legacyApi) {
        disconnectLegacySignals();
        delete m_legacyApi;
    }
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "LegacyApiAdapter 销毁");
}

bool LegacyApiAdapter::initialize(bool useNewEngine)
{
    if (m_initialized) {
        NEW_LOG_WARNING(NewLogCategory::SYSTEM, "LegacyApiAdapter 已经初始化");
        return true;
    }

    m_useNewEngine = useNewEngine;

    // 创建遗留API实例
    m_legacyApi = new OrderAPI(this);
    if (!m_legacyApi) {
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, "创建 OrderAPI 实例失败");
        return false;
    }

    // 连接遗留API的信号
    connectLegacySignals();

    m_initialized = true;
    NEW_LOG_INFO(NewLogCategory::SYSTEM, QString("LegacyApiAdapter 初始化完成，使用%1引擎")
                 .arg(m_useNewEngine ? "新" : "遗留"));

    return true;
}

void LegacyApiAdapter::setNetworkEngine(INetworkEngine* engine)
{
    m_networkEngine = engine;
    if (engine) {
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("设置网络引擎: %1").arg(engine->getEngineName()));
    }
}

void LegacyApiAdapter::loginPreCheck(const QString& account, const QString& proxy, 
                                    std::function<void(const QString&)> callback)
{
    if (!m_initialized || !m_legacyApi) {
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, "LegacyApiAdapter 未初始化");
        callback("{\"success\":false,\"message\":\"适配器未初始化\"}");
        return;
    }

    NEW_LOG_DEBUG(NewLogCategory::API, QString("登录预检: %1").arg(account));
    
    // 目前直接使用遗留API
    m_legacyApi->loginPreCheck(account, proxy, callback);
}

void LegacyApiAdapter::login(const QString& account, const QString& password, 
                            const QString& proxy, std::function<void(const QString&)> callback)
{
    if (!m_initialized || !m_legacyApi) {
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, "LegacyApiAdapter 未初始化");
        callback("{\"success\":false,\"message\":\"适配器未初始化\"}");
        return;
    }

    NEW_LOG_DEBUG(NewLogCategory::API, QString("用户登录: %1").arg(account));
    
    // 目前直接使用遗留API
    m_legacyApi->login(account, password, proxy, callback);
}

void LegacyApiAdapter::refreshOrders(const QString &gameId, const QString &token, const QString &userId,
                                    const QString &proxyHost, int proxyPort,
                                    const QString &proxyType, const QString &proxyUser, const QString &proxyPass,
                                    const QString &priceStr, int focusedFlag)
{
    if (!m_initialized || !m_legacyApi) {
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, "LegacyApiAdapter 未初始化");
        emit networkError("适配器未初始化");
        return;
    }

    NEW_LOG_DEBUG(NewLogCategory::API, QString("刷新订单: gameId=%1, userId=%2").arg(gameId, userId));
    
    // 目前直接使用遗留API
    m_legacyApi->refreshOrders(gameId, token, userId, proxyHost, proxyPort, 
                              proxyType, proxyUser, proxyPass, priceStr, focusedFlag);
}

void LegacyApiAdapter::acceptOrder(const QString &orderId, const QString &stamp, const QString &token,
                                  const QString &userId, const QString &payPassword, const QString &loginId,
                                  const QString &proxyHost, int proxyPort,
                                  const QString &proxyUser, const QString &proxyPass)
{
    if (!m_initialized || !m_legacyApi) {
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, "LegacyApiAdapter 未初始化");
        emit orderAcceptResult(false, "适配器未初始化", orderId);
        return;
    }

    NEW_LOG_DEBUG(NewLogCategory::API, QString("接受订单: %1").arg(orderId));
    
    // 目前直接使用遗留API
    m_legacyApi->acceptOrder(orderId, stamp, token, userId, payPassword, loginId,
                            proxyHost, proxyPort, proxyUser, proxyPass);
}

void LegacyApiAdapter::getUserInfo(const QString &token, const QString &userId,
                                  const QString &proxyHost, int proxyPort)
{
    if (!m_initialized || !m_legacyApi) {
        NEW_LOG_ERROR(NewLogCategory::SYSTEM, "LegacyApiAdapter 未初始化");
        emit userInfoResult(false, "适配器未初始化", QJsonObject());
        return;
    }

    NEW_LOG_DEBUG(NewLogCategory::API, QString("获取用户信息: %1").arg(userId));
    
    // 目前直接使用遗留API
    m_legacyApi->getUserInfo(token, userId, proxyHost, proxyPort);
}

void LegacyApiAdapter::setPageSize(int pageSize)
{
    if (m_legacyApi) {
        m_legacyApi->setPageSize(pageSize);
        NEW_LOG_DEBUG(NewLogCategory::API, QString("设置页面大小: %1").arg(pageSize));
    }
}

QString LegacyApiAdapter::getCurrentEngineType() const
{
    if (m_useNewEngine && m_networkEngine) {
        return m_networkEngine->getEngineName();
    } else if (m_legacyApi) {
        return QString("Legacy-%1").arg(
            m_legacyApi->getNetworkEngine() == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS" : "curl");
    }
    return "Unknown";
}

void LegacyApiAdapter::connectLegacySignals()
{
    if (!m_legacyApi) return;

    // 连接信号到槽函数（使用Lambda适配器处理参数差异）
    connect(m_legacyApi, &OrderAPI::loginResult, this, 
            [this](bool success, const QString& message, const QString& token, const QString& userId, const QString& uid) {
                Q_UNUSED(userId)  // 在Lambda中处理未使用参数
                Q_UNUSED(uid)
                this->onLegacyLoginResult(success, message, token);
            });
    
    connect(m_legacyApi, &OrderAPI::orderRefreshResult, this,
            [this](bool success, const QString& message, const QJsonArray& orders, int recordCount) {
                Q_UNUSED(recordCount)  // 记录数暂时不需要
                this->onLegacyOrderRefreshResult(success, message, orders);
            });
    
    connect(m_legacyApi, &OrderAPI::orderAcceptResult, this, &LegacyApiAdapter::onLegacyOrderAcceptResult);
    connect(m_legacyApi, &OrderAPI::userInfoResult, this, &LegacyApiAdapter::onLegacyUserInfoResult);
    connect(m_legacyApi, &OrderAPI::networkError, this, &LegacyApiAdapter::onLegacyNetworkError);
    connect(m_legacyApi, &OrderAPI::debugLog, this, &LegacyApiAdapter::onLegacyDebugLog);
    connect(m_legacyApi, &OrderAPI::fastOrderFound, this, &LegacyApiAdapter::onLegacyFastOrderFound);
}

void LegacyApiAdapter::disconnectLegacySignals()
{
    if (!m_legacyApi) return;

    disconnect(m_legacyApi, nullptr, this, nullptr);
}

// 信号转发槽函数（优化后的参数列表）
void LegacyApiAdapter::onLegacyLoginResult(bool success, const QString& message, const QString& token)
{
    // 直接使用所有参数，无需Q_UNUSED
    emit loginResult(success, message, token);
}

void LegacyApiAdapter::onLegacyOrderRefreshResult(bool success, const QString& message, const QJsonArray& orders)
{
    // 直接使用所有参数，无需Q_UNUSED
    emit orderRefreshResult(success, message, orders);
}

void LegacyApiAdapter::onLegacyOrderAcceptResult(bool success, const QString& message)  
{
    // 发出适配后的信号，添加空的orderId参数保持兼容
    emit orderAcceptResult(success, message, QString());
}

void LegacyApiAdapter::onLegacyUserInfoResult(bool success, const QString& message, const QJsonObject& userInfo)
{
    emit userInfoResult(success, message, userInfo);
}

void LegacyApiAdapter::onLegacyNetworkError(const QString& error)
{
    emit networkError(error);
}

void LegacyApiAdapter::onLegacyDebugLog(const QString& message)
{
    // 转发调试日志
    emit debugLog(message);
}

void LegacyApiAdapter::onLegacyFastOrderFound(const QJsonObject& orderObj)
{
    // 转换为字符串格式发出
    QString orderInfo = QJsonDocument(orderObj).toJson(QJsonDocument::Compact);
    emit fastOrderFound(orderInfo);
}

QString LegacyApiAdapter::buildPostData(const QList<QPair<QString, QString>> &params)
{
    QString result;
    bool isFirst = true;
    for (const auto &pair : params) {
        if (!isFirst) {
            result.append('&');
        }
        result.append(pair.first);
        result.append('=');
        result.append(pair.second);
        isFirst = false;
    }
    return result;
}

QString LegacyApiAdapter::signstr(const QString &action, const QString &data, const QString &token)
{
    // 这里可以复用OrderAPI的签名逻辑，或者实现新的签名方法
    // 目前先返回空字符串，后续可以完善
    Q_UNUSED(action)
    Q_UNUSED(data)
    Q_UNUSED(token)
    return QString();
}
