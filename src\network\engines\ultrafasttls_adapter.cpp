#include "ultrafasttls_adapter.h"
#include <logger.h>
#include <QMutexLocker>

UltraFastTLSAdapter::UltraFastTLSAdapter(QObject* parent)
    : INetworkEngine(parent)
    , m_ultraFastTLS(nullptr)
    , m_initialized(false)
    , m_isHealthy(false)
    , m_healthCheckTimer(new QTimer(this))
{
    m_stats.startTime = QDateTime::currentDateTime();
    
    // 连接健康检查定时器
    connect(m_healthCheckTimer, &QTimer::timeout, this, &UltraFastTLSAdapter::onHealthCheckTimer);
    
    NEW_LOG_INFO(NewLogCategory::NETWORK, "UltraFastTLSAdapter created");
}

UltraFastTLSAdapter::~UltraFastTLSAdapter()
{
    cleanup();
    NEW_LOG_INFO(NewLogCategory::NETWORK, "UltraFastTLSAdapter destroyed");
}

QString UltraFastTLSAdapter::getEngineName() const
{
    return "UltraFastTLS";
}

bool UltraFastTLSAdapter::isHealthy() const
{
    return m_isHealthy && m_ultraFastTLS && m_initialized;
}

qint64 UltraFastTLSAdapter::getAverageResponseTime() const
{
    QMutexLocker locker(&m_mutex);
    return m_stats.totalRequests > 0 ? m_stats.totalResponseTime / m_stats.totalRequests : 0;
}

int UltraFastTLSAdapter::getRequestCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_stats.totalRequests;
}

void UltraFastTLSAdapter::setTimeout(int timeoutMs)
{
    QMutexLocker locker(&m_mutex);
    m_config.timeout = timeoutMs;
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("Timeout set to %1ms").arg(timeoutMs));
}

void UltraFastTLSAdapter::setUserAgent(const QString& userAgent)
{
    QMutexLocker locker(&m_mutex);
    m_config.userAgent = userAgent;
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, QString("User agent set to: %1").arg(userAgent));
}

void UltraFastTLSAdapter::setProxy(const QString& /* host */, int /* port */, const QString& /* type */)
{
    // 使用注释参数名表示有意不使用，比Q_UNUSED更清晰
    NEW_LOG_WARNING(NewLogCategory::NETWORK, "Proxy configuration not supported by UltraFastTLS");
}

bool UltraFastTLSAdapter::initialize()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    try {
        m_ultraFastTLS = new UltraFastTLS();
        
        // 设置默认的浏览器指纹
        m_ultraFastTLS->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);
        
        // 启动健康检查定时器
        m_healthCheckTimer->start(30000); // 30秒检查一次
        
        m_initialized = true;
        m_isHealthy = true;
        m_lastError.clear();
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, "UltraFastTLS adapter initialized successfully");
        emit healthStatusChanged(true);
        
        return true;
        
    } catch (const std::exception& e) {
        m_lastError = QString("UltraFastTLS initialization failed: %1").arg(e.what());
        NEW_LOG_ERROR(NewLogCategory::NETWORK, m_lastError);
        m_isHealthy = false;
        emit healthStatusChanged(false);
        return false;
    }
}

void UltraFastTLSAdapter::cleanup()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_healthCheckTimer) {
        m_healthCheckTimer->stop();
    }
    
    if (m_ultraFastTLS) {
        delete m_ultraFastTLS;
        m_ultraFastTLS = nullptr;
    }
    
    m_initialized = false;
    m_isHealthy = false;
    
    NEW_LOG_INFO(NewLogCategory::NETWORK, "UltraFastTLS adapter cleaned up");
}

NetworkResult UltraFastTLSAdapter::executeRequest(const QString& url,
                                                 const QString& postData,
                                                 const QJsonObject& /* headers */)
{
    // 使用注释参数名，暂时不支持自定义headers
    
    if (!m_initialized || !m_isHealthy || !m_ultraFastTLS) {
        m_lastError = "Engine not healthy or not initialized";
        return createNetworkResult("", false, m_lastError);
    }
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    QMutexLocker locker(&m_mutex);
    
    try {
        m_stats.totalRequests++;
        
        // 执行请求
        QString response = m_ultraFastTLS->executeRequest(url, postData);
        
        // 计算响应时间
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        // 创建结果
        NetworkResult result = createNetworkResult(response, true);
        result.responseTime = duration.count();
        
        // 更新统计
        updatePerformanceStats(result);
        m_stats.successfulRequests++;
        
        emit requestCompleted(result);
        return result;
        
    } catch (const std::exception& e) {
        m_stats.failedRequests++;
        m_lastError = QString("UltraFastTLS request failed: %1").arg(e.what());
        
        NetworkResult result = createNetworkResult("", false, m_lastError);
        emit requestFailed(m_lastError);
        return result;
        
    } catch (...) {
        m_stats.failedRequests++;
        m_lastError = "UltraFastTLS request failed: unknown error";
        
        NetworkResult result = createNetworkResult("", false, m_lastError);
        emit requestFailed(m_lastError);
        return result;
    }
}

void UltraFastTLSAdapter::setConfig(const AdapterConfig& config)
{
    QMutexLocker locker(&m_mutex);
    m_config = config;
    NEW_LOG_INFO(NewLogCategory::NETWORK, "UltraFastTLS adapter config updated");
}

UltraFastTLSAdapter::AdapterConfig UltraFastTLSAdapter::getConfig() const
{
    QMutexLocker locker(&m_mutex);
    return m_config;
}

void UltraFastTLSAdapter::onHealthCheckTimer()
{
    performHealthCheck();
}

void UltraFastTLSAdapter::performHealthCheck()
{
    if (!m_ultraFastTLS || !m_initialized) {
        if (m_isHealthy) {
            m_isHealthy = false;
            emit healthStatusChanged(m_isHealthy);
        }
        return;
    }
    
    // 执行健康检查逻辑
    bool wasHealthy = m_isHealthy;
    m_isHealthy = (m_ultraFastTLS != nullptr);  // 简单的健康检查
    
    if (wasHealthy != m_isHealthy) {
        emit healthStatusChanged(m_isHealthy);
    }
}

void UltraFastTLSAdapter::updatePerformanceStats(const NetworkResult& result)
{
    m_stats.totalResponseTime += result.responseTime;
}

NetworkResult UltraFastTLSAdapter::createNetworkResult(const QString& data, bool success, const QString& error)
{
    NetworkResult result;
    result.success = success;
    result.data = data;
    result.responseData = data;
    result.errorMessage = error;
    result.engineName = getEngineName();
    result.timestamp = QDateTime::currentDateTime();
    return result;
}
