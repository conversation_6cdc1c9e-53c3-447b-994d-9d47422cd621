#ifndef ULTRAFASTTLS_ADAPTER_H
#define ULTRAFASTTLS_ADAPTER_H

#include "network_engine_interface.h"
#include <ultrafasttls.h>
#include <QString>
#include <QMutex>
#include <QTimer>
#include <QJsonObject>
#include <chrono>

/**
 * @brief UltraFastTLS网络引擎适配器
 * 
 * 将UltraFastTLS库适配到统一的INetworkEngine接口
 */
class UltraFastTLSAdapter : public INetworkEngine
{
    Q_OBJECT

public:
    explicit UltraFastTLSAdapter(QObject* parent = nullptr);
    ~UltraFastTLSAdapter() override;

    // INetworkEngine 接口实现
    bool initialize() override;
    void cleanup() override;
    NetworkResult executeRequest(const QString& url, 
                                const QString& postData, 
                                const QJsonObject& headers) override;

    // 性能接口实现
    QString getEngineName() const override;
    qint64 getAverageResponseTime() const override;
    int getRequestCount() const override;
    
    // 配置接口实现
    void setTimeout(int timeoutMs) override;
    void setUserAgent(const QString& userAgent) override;
    void setProxy(const QString& host, int port, const QString& type = "http") override;

    // 适配器特定配置
    struct AdapterConfig {
        int timeout = 30000;            // 超时时间(ms)
        int maxRetries = 3;             // 最大重试次数
        bool enableCompression = true;  // 启用压缩
        bool enableKeepAlive = true;    // 启用Keep-Alive
        QString userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
    };

    void setConfig(const AdapterConfig& config);
    AdapterConfig getConfig() const;

    // 健康状态查询
    bool isHealthy() const;

signals:
    void healthStatusChanged(bool isHealthy);

private slots:
    void onHealthCheckTimer();

private:
    void updatePerformanceStats(const NetworkResult& result);
    NetworkResult createNetworkResult(const QString& data, bool success, const QString& error = QString());
    void performHealthCheck();

    UltraFastTLS* m_ultraFastTLS;
    AdapterConfig m_config;
    
    // 状态管理
    bool m_initialized;
    bool m_isHealthy;
    QString m_lastError;
    
    // 统计信息
    struct {
        int totalRequests = 0;
        int successfulRequests = 0;
        int failedRequests = 0;
        qint64 totalResponseTime = 0;
        QDateTime startTime;
    } m_stats;
    
    // 健康检查
    QTimer* m_healthCheckTimer;
    
    // 线程安全
    mutable QMutex m_mutex;
};

#endif // ULTRAFASTTLS_ADAPTER_H
