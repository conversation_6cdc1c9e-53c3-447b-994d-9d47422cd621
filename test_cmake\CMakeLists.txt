cmake_minimum_required(VERSION 3.21)
project(OpenSSLTest)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Check for OpenSSL
set(OPENSSL_SEARCH_PATHS
    "C:/Program Files/OpenSSL-Win64"
    "C:/OpenSSL-Win64"
    "C:/Users/<USER>/Desktop/vcpkg-master/vcpkg-master/installed/x64-windows"
)

set(OPENSSL_FOUND FALSE)

foreach(SEARCH_PATH ${OPENSSL_SEARCH_PATHS})
    if(EXISTS "${SEARCH_PATH}/include/openssl/ssl.h")
        set(OPENSSL_ROOT_DIR "${SEARCH_PATH}")
        set(OPENSSL_INCLUDE_DIR "${SEARCH_PATH}/include")

        # Find libraries
        find_library(OPENSSL_SSL_LIBRARY
            NAMES libssl ssl
            PATHS 
                "${SEARCH_PATH}/lib/VC/x64/MD"
                "${SEARCH_PATH}/lib/VC/x64/MT" 
                "${SEARCH_PATH}/lib"
            NO_DEFAULT_PATH)
        find_library(OPENSSL_CRYPTO_LIBRARY
            NAMES libcrypto crypto
            PATHS 
                "${SEARCH_PATH}/lib/VC/x64/MD"
                "${SEARCH_PATH}/lib/VC/x64/MT"
                "${SEARCH_PATH}/lib"
            NO_DEFAULT_PATH)

        if(OPENSSL_SSL_LIBRARY AND OPENSSL_CRYPTO_LIBRARY)
            set(OPENSSL_LIBRARIES ${OPENSSL_SSL_LIBRARY} ${OPENSSL_CRYPTO_LIBRARY})
            set(OPENSSL_FOUND TRUE)
            message(STATUS "OpenSSL found at: ${SEARCH_PATH}")
            message(STATUS "SSL Library: ${OPENSSL_SSL_LIBRARY}")
            message(STATUS "Crypto Library: ${OPENSSL_CRYPTO_LIBRARY}")
            break()
        endif()
    endif()
endforeach()

# Create executable
add_executable(openssl_test ../test_openssl.cpp)

if(OPENSSL_FOUND)
    target_compile_definitions(openssl_test PRIVATE OPENSSL_FOUND)
    target_include_directories(openssl_test PRIVATE ${OPENSSL_INCLUDE_DIR})
    target_link_libraries(openssl_test PRIVATE ${OPENSSL_LIBRARIES})
    message(STATUS "Building with OpenSSL support")
else()
    message(STATUS "Building without OpenSSL (fallback mode)")
endif()

# Windows specific libraries
if(WIN32)
    target_link_libraries(openssl_test PRIVATE ws2_32 crypt32)
endif()
