@echo off
echo 🔧 测试渐进式重组编译状态...
echo.

REM 设置Qt环境
set PATH=C:\Qt\6.9.1\mingw_64\bin;C:\Qt\Tools\mingw1310_64\bin;C:\Qt\Tools\CMake_64\bin;%PATH%

echo 📁 当前目录: %CD%
echo 🎯 开始编译测试...
echo.

REM 进入构建目录
cd build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug

REM 运行cmake构建
cmake --build . --config Debug --target OrderManager -j 2

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 编译成功！渐进式重组架构正常工作
    echo 📊 重组进展：第一阶段完成，适配器架构已建立
) else (
    echo.
    echo ❌ 编译失败，错误代码: %ERRORLEVEL%
    echo 🔍 需要进一步修复包含路径或依赖关系
)

echo.
echo 🏁 编译测试完成
pause
